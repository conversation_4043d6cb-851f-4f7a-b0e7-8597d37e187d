package models

import (
	"time"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	Email             string    `json:"email" gorm:"uniqueIndex;not null"`
	Password          string    `json:"-" gorm:"not null"`
	IsAdmin           bool      `json:"is_admin" gorm:"default:false"`
	MaxLicenseQuota   int       `json:"max_license_quota" gorm:"default:0"`
	UsedLicenseQuota  int       `json:"used_license_quota" gorm:"default:0"`
	IsActive          bool      `json:"is_active" gorm:"default:true"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`
}

// Product 产品模型
type Product struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// LicenseRequest 授权申请记录
type LicenseRequest struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	UserID          uint      `json:"user_id" gorm:"not null"`
	ProductID       uint      `json:"product_id" gorm:"not null"`
	MachineCode     string    `json:"machine_code" gorm:"not null"`
	LicenseCode     string    `json:"license_code" gorm:"not null"`
	MachineName     string    `json:"machine_name"`
	CompanyName     string    `json:"company_name"`
	ContactPhone    string    `json:"contact_phone"`
	OSType          string    `json:"os_type"`
	InstallTime     *time.Time `json:"install_time"`
	Remarks         string    `json:"remarks"`
	Status          string    `json:"status" gorm:"default:active"` // active, revoked
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	
	// 关联
	User    User    `json:"user" gorm:"foreignKey:UserID"`
	Product Product `json:"product" gorm:"foreignKey:ProductID"`
}

// AdminLog 管理员操作日志
type AdminLog struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	AdminID     uint      `json:"admin_id" gorm:"not null"`
	TargetUserID *uint    `json:"target_user_id"`
	Action      string    `json:"action" gorm:"not null"`
	Description string    `json:"description"`
	OldValue    string    `json:"old_value"`
	NewValue    string    `json:"new_value"`
	IPAddress   string    `json:"ip_address"`
	CreatedAt   time.Time `json:"created_at"`
	
	// 关联
	Admin      User  `json:"admin" gorm:"foreignKey:AdminID"`
	TargetUser *User `json:"target_user,omitempty" gorm:"foreignKey:TargetUserID"`
}

// SystemConfig 系统配置
type SystemConfig struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Key       string    `json:"key" gorm:"uniqueIndex;not null"`
	Value     string    `json:"value"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// 获取用户剩余配额
func (u *User) GetRemainingQuota() int {
	return u.MaxLicenseQuota - u.UsedLicenseQuota
}

// 检查是否可以申请授权
func (u *User) CanRequestLicense() bool {
	return u.IsActive && u.GetRemainingQuota() > 0
}
