<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 sm:mx-0 sm:h-10 sm:w-10">
              <UserPlusIcon class="h-6 w-6 text-primary-600" />
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                创建新用户
              </h3>
              <div class="mt-4">
                <form @submit.prevent="handleSubmit" class="space-y-4">
                  <div>
                    <label class="form-label">邮箱地址 *</label>
                    <input
                      v-model="form.email"
                      type="email"
                      required
                      class="form-input"
                      placeholder="请输入邮箱地址"
                    />
                  </div>

                  <div>
                    <label class="form-label">最大授权配额 *</label>
                    <input
                      v-model.number="form.max_license_quota"
                      type="number"
                      required
                      min="0"
                      class="form-input"
                      placeholder="请输入最大可申请授权数量"
                    />
                    <p class="mt-1 text-sm text-gray-500">
                      用户最多可以申请的授权码数量
                    </p>
                  </div>

                  <div class="flex items-center">
                    <input
                      v-model="form.is_admin"
                      type="checkbox"
                      class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label class="ml-2 block text-sm text-gray-900">
                      设为管理员
                    </label>
                  </div>

                  <div v-if="error" class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">创建失败</h3>
                        <div class="mt-2 text-sm text-red-700">{{ error }}</div>
                      </div>
                    </div>
                  </div>

                  <div v-if="success" class="rounded-md bg-green-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <CheckCircleIcon class="h-5 w-5 text-green-400" />
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">创建成功</h3>
                        <div class="mt-2 text-sm text-green-700">
                          <p>用户已创建，临时密码为：</p>
                          <div class="mt-2 flex items-center">
                            <code class="bg-white px-2 py-1 rounded border font-mono">
                              {{ generatedPassword }}
                            </code>
                            <button
                              type="button"
                              @click="copyPassword"
                              class="ml-2 text-green-600 hover:text-green-800"
                              title="复制密码"
                            >
                              <ClipboardIcon class="h-4 w-4" />
                            </button>
                          </div>
                          <p class="mt-2 text-xs">请将此密码告知用户，建议用户首次登录后立即修改密码。</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            v-if="!success"
            @click="handleSubmit"
            :disabled="loading"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
          >
            <span v-if="loading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              创建中...
            </span>
            <span v-else>创建用户</span>
          </button>
          <button
            @click="$emit('close')"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            {{ success ? '关闭' : '取消' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import api from '@/utils/api'
import type { CreateUserRequest, ApiResponse } from '@/types'
import { 
  UserPlusIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon,
  ClipboardIcon 
} from '@heroicons/vue/24/outline'

defineEmits<{
  close: []
  success: []
}>()

const loading = ref(false)
const error = ref('')
const success = ref(false)
const generatedPassword = ref('')

const form = reactive<CreateUserRequest>({
  email: '',
  max_license_quota: 10,
  is_admin: false
})

const handleSubmit = async () => {
  if (loading.value) return
  
  loading.value = true
  error.value = ''
  
  try {
    const response = await api.post<ApiResponse<{ user: any; password: string }>>('/admin/users', form)
    
    if (response.data.success && response.data.data) {
      success.value = true
      generatedPassword.value = response.data.data.password
      
      // 3秒后自动触发成功事件
      setTimeout(() => {
        // $emit('success')
      }, 3000)
    } else {
      error.value = response.data.error || '创建失败'
    }
  } catch (err: any) {
    error.value = err.response?.data?.error || '创建失败，请检查输入信息'
  } finally {
    loading.value = false
  }
}

const copyPassword = async () => {
  try {
    await navigator.clipboard.writeText(generatedPassword.value)
    // 这里可以添加一个toast提示
  } catch (error) {
    console.error('Failed to copy password:', error)
  }
}
</script>
