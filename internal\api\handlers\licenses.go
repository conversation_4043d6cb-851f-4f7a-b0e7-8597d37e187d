package handlers

import (
	"net/http"
	"strconv"
	"time"
	"licmanager/internal/models"
	"licmanager/internal/utils"

	"github.com/gin-gonic/gin"
)

// RequestLicenseRequest 申请授权请求结构
type RequestLicenseRequest struct {
	ProductID    uint       `json:"product_id" binding:"required"`
	MachineCode  string     `json:"machine_code" binding:"required"`
	MachineName  string     `json:"machine_name"`
	CompanyName  string     `json:"company_name"`
	ContactPhone string     `json:"contact_phone"`
	OSType       string     `json:"os_type"`
	InstallTime  *time.Time `json:"install_time"`
	Remarks      string     `json:"remarks"`
}

// RequestLicense 申请授权码
func (h *Handlers) RequestLicense(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req RequestLicenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	// 验证机器码格式
	if !utils.ValidateMachineCode(req.MachineCode) {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid machine code format"))
		return
	}

	// 获取用户信息
	var user models.User
	if err := h.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("User not found"))
		return
	}

	// 检查用户是否可以申请授权
	if !user.CanRequestLicense() {
		c.JSON(http.StatusBadRequest, ErrorResponse("Insufficient license quota"))
		return
	}

	// 验证产品是否存在且有效
	var product models.Product
	if err := h.DB.Where("id = ? AND is_active = ?", req.ProductID, true).First(&product).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("Product not found"))
		return
	}

	// 检查该机器码是否已经申请过授权
	var existingLicense models.LicenseRequest
	if err := h.DB.Where("machine_code = ? AND product_id = ? AND status = ?", 
		req.MachineCode, req.ProductID, "active").First(&existingLicense).Error; err == nil {
		c.JSON(http.StatusConflict, ErrorResponse("License already exists for this machine"))
		return
	}

	// 生成授权码
	licenseCode := utils.GenerateLicenseCode(req.MachineCode, req.ProductID, userID)

	// 创建授权记录
	license := models.LicenseRequest{
		UserID:       userID,
		ProductID:    req.ProductID,
		MachineCode:  req.MachineCode,
		LicenseCode:  licenseCode,
		MachineName:  req.MachineName,
		CompanyName:  req.CompanyName,
		ContactPhone: req.ContactPhone,
		OSType:       req.OSType,
		InstallTime:  req.InstallTime,
		Remarks:      req.Remarks,
		Status:       "active",
	}

	// 开始事务
	tx := h.DB.Begin()

	// 创建授权记录
	if err := tx.Create(&license).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to create license"))
		return
	}

	// 更新用户已使用配额
	if err := tx.Model(&user).Update("used_license_quota", user.UsedLicenseQuota+1).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to update user quota"))
		return
	}

	// 提交事务
	tx.Commit()

	// 加载关联数据
	h.DB.Preload("Product").First(&license, license.ID)

	c.JSON(http.StatusCreated, SuccessMessageResponse("License created successfully", license))
}

// GetUserLicenses 获取用户的授权记录
func (h *Handlers) GetUserLicenses(c *gin.Context) {
	userID := c.GetUint("user_id")

	var pagination PaginationQuery
	if err := c.ShouldBindQuery(&pagination); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid pagination parameters"))
		return
	}

	var licenses []models.LicenseRequest
	var total int64

	// 计算总数
	h.DB.Model(&models.LicenseRequest{}).Where("user_id = ?", userID).Count(&total)

	// 获取分页数据
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := h.DB.Preload("Product").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&licenses).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to fetch licenses"))
		return
	}

	meta := CalculatePaginationMeta(pagination.Page, pagination.PerPage, total)
	c.JSON(http.StatusOK, PaginationSuccessResponse(licenses, meta))
}

// GetLicense 获取单个授权记录详情
func (h *Handlers) GetLicense(c *gin.Context) {
	userID := c.GetUint("user_id")
	isAdmin := c.GetBool("is_admin")

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid license ID"))
		return
	}

	var license models.LicenseRequest
	query := h.DB.Preload("Product").Preload("User")

	// 非管理员只能查看自己的授权记录
	if !isAdmin {
		query = query.Where("user_id = ?", userID)
	}

	if err := query.First(&license, id).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("License not found"))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(license))
}

// GetAllLicenses 获取所有授权记录 (管理员)
func (h *Handlers) GetAllLicenses(c *gin.Context) {
	var pagination PaginationQuery
	if err := c.ShouldBindQuery(&pagination); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid pagination parameters"))
		return
	}

	var licenses []models.LicenseRequest
	var total int64

	// 计算总数
	h.DB.Model(&models.LicenseRequest{}).Count(&total)

	// 获取分页数据
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := h.DB.Preload("Product").Preload("User").
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&licenses).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to fetch licenses"))
		return
	}

	meta := CalculatePaginationMeta(pagination.Page, pagination.PerPage, total)
	c.JSON(http.StatusOK, PaginationSuccessResponse(licenses, meta))
}

// RevokeLicense 撤销授权 (管理员)
func (h *Handlers) RevokeLicense(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid license ID"))
		return
	}

	var license models.LicenseRequest
	if err := h.DB.Preload("User").First(&license, id).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("License not found"))
		return
	}

	if license.Status == "revoked" {
		c.JSON(http.StatusBadRequest, ErrorResponse("License already revoked"))
		return
	}

	// 开始事务
	tx := h.DB.Begin()

	// 更新授权状态
	if err := tx.Model(&license).Update("status", "revoked").Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to revoke license"))
		return
	}

	// 减少用户已使用配额
	if err := tx.Model(&license.User).Update("used_license_quota", license.User.UsedLicenseQuota-1).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to update user quota"))
		return
	}

	// 提交事务
	tx.Commit()

	// 记录管理员操作日志
	h.logAdminAction(c, "REVOKE_LICENSE", "Revoked license: "+license.LicenseCode, "active", "revoked")

	c.JSON(http.StatusOK, SuccessMessageResponse("License revoked successfully", nil))
}
