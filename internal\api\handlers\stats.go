package handlers

import (
	"licmanager/internal/models"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// DashboardStats 仪表板统计数据
type DashboardStats struct {
	RemainingQuota int `json:"remaining_quota"`
	UsedQuota      int `json:"used_quota"`
	TotalQuota     int `json:"total_quota"`
	TotalLicenses  int `json:"total_licenses"`
	ActiveLicenses int `json:"active_licenses"`
}

// GetDashboardStats 获取用户仪表板统计
func (h *Handlers) GetDashboardStats(c *gin.Context) {
	userID := c.GetUint("user_id")

	var user models.User
	if err := h.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("User not found"))
		return
	}

	var totalLicenses, activeLicenses int64
	h.DB.Model(&models.LicenseRequest{}).Where("user_id = ?", userID).Count(&totalLicenses)
	h.DB.Model(&models.LicenseRequest{}).Where("user_id = ? AND status = ?", userID, "active").Count(&activeLicenses)

	stats := DashboardStats{
		RemainingQuota: user.GetRemainingQuota(),
		UsedQuota:      user.UsedLicenseQuota,
		TotalQuota:     user.MaxLicenseQuota,
		TotalLicenses:  int(totalLicenses),
		ActiveLicenses: int(activeLicenses),
	}

	c.JSON(http.StatusOK, SuccessResponse(stats))
}

// AdminStats 管理员统计数据
type AdminStats struct {
	TotalUsers        int64 `json:"total_users"`
	ActiveUsers       int64 `json:"active_users"`
	TotalProducts     int64 `json:"total_products"`
	TotalLicenses     int64 `json:"total_licenses"`
	ActiveLicenses    int64 `json:"active_licenses"`
	TotalQuotaIssued  int64 `json:"total_quota_issued"`
	TotalQuotaUsed    int64 `json:"total_quota_used"`
	NewUsersThisMonth int64 `json:"new_users_this_month"`
}

// GetAdminStats 获取管理员统计概览
func (h *Handlers) GetAdminStats(c *gin.Context) {
	var stats AdminStats

	// 用户统计
	h.DB.Model(&models.User{}).Count(&stats.TotalUsers)
	h.DB.Model(&models.User{}).Where("is_active = ?", true).Count(&stats.ActiveUsers)

	// 产品统计
	h.DB.Model(&models.Product{}).Where("is_active = ?", true).Count(&stats.TotalProducts)

	// 授权统计
	h.DB.Model(&models.LicenseRequest{}).Count(&stats.TotalLicenses)
	h.DB.Model(&models.LicenseRequest{}).Where("status = ?", "active").Count(&stats.ActiveLicenses)

	// 配额统计
	h.DB.Model(&models.User{}).Select("COALESCE(SUM(max_license_quota), 0)").Scan(&stats.TotalQuotaIssued)
	h.DB.Model(&models.User{}).Select("COALESCE(SUM(used_license_quota), 0)").Scan(&stats.TotalQuotaUsed)

	// 本月新用户
	startOfMonth := time.Now().AddDate(0, 0, -time.Now().Day()+1)
	h.DB.Model(&models.User{}).Where("created_at >= ?", startOfMonth).Count(&stats.NewUsersThisMonth)

	c.JSON(http.StatusOK, SuccessResponse(stats))
}

// UserStatsItem 用户统计项
type UserStatsItem struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

// GetUserStats 获取用户注册统计
func (h *Handlers) GetUserStats(c *gin.Context) {
	days := c.DefaultQuery("days", "30")

	var stats []UserStatsItem

	// 获取最近N天的用户注册统计
	query := `
		SELECT DATE(created_at) as date, COUNT(*) as count 
		FROM users 
		WHERE created_at >= date('now', '-` + days + ` days')
		GROUP BY DATE(created_at)
		ORDER BY date
	`

	h.DB.Raw(query).Scan(&stats)

	c.JSON(http.StatusOK, SuccessResponse(stats))
}

// LicenseStatsItem 授权统计项
type LicenseStatsItem struct {
	Date   string `json:"date"`
	Count  int    `json:"count"`
	Status string `json:"status"`
}

// GetLicenseStats 获取授权申请统计
func (h *Handlers) GetLicenseStats(c *gin.Context) {
	days := c.DefaultQuery("days", "30")

	var stats []LicenseStatsItem

	// 获取最近N天的授权申请统计
	query := `
		SELECT DATE(created_at) as date, COUNT(*) as count, status
		FROM license_requests 
		WHERE created_at >= date('now', '-` + days + ` days')
		GROUP BY DATE(created_at), status
		ORDER BY date, status
	`

	h.DB.Raw(query).Scan(&stats)

	c.JSON(http.StatusOK, SuccessResponse(stats))
}

// GetAdminLogs 获取管理员操作日志
func (h *Handlers) GetAdminLogs(c *gin.Context) {
	var pagination PaginationQuery
	if err := c.ShouldBindQuery(&pagination); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid pagination parameters"))
		return
	}

	var logs []models.AdminLog
	var total int64

	// 计算总数
	h.DB.Model(&models.AdminLog{}).Count(&total)

	// 获取分页数据
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := h.DB.Preload("Admin").Preload("TargetUser").
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&logs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to fetch admin logs"))
		return
	}

	meta := CalculatePaginationMeta(pagination.Page, pagination.PerPage, total)
	c.JSON(http.StatusOK, PaginationSuccessResponse(logs, meta))
}

// logAdminAction 记录管理员操作日志
func (h *Handlers) logAdminAction(c *gin.Context, action, description, oldValue, newValue string) {
	adminID := c.GetUint("user_id")
	ipAddress := c.ClientIP()

	log := models.AdminLog{
		AdminID:     adminID,
		Action:      action,
		Description: description,
		OldValue:    oldValue,
		NewValue:    newValue,
		IPAddress:   ipAddress,
	}

	h.DB.Create(&log)
}

// logAdminActionWithTarget 记录带目标用户的管理员操作日志
func (h *Handlers) logAdminActionWithTarget(c *gin.Context, targetUserID uint, action, description, oldValue, newValue string) {
	adminID := c.GetUint("user_id")
	ipAddress := c.ClientIP()

	log := models.AdminLog{
		AdminID:      adminID,
		TargetUserID: &targetUserID,
		Action:       action,
		Description:  description,
		OldValue:     oldValue,
		NewValue:     newValue,
		IPAddress:    ipAddress,
	}

	h.DB.Create(&log)
}
