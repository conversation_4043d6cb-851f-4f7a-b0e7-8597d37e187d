import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'
import type { User, LoginRequest, LoginResponse, ApiResponse } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(null)
  const user = ref<User | null>(null)
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.is_admin || false)

  // 初始化认证状态
  const initializeAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)
    }
  }

  // 登录
  const login = async (credentials: LoginRequest): Promise<void> => {
    loading.value = true
    try {
      const response = await api.post<ApiResponse<LoginResponse>>('/auth/login', credentials)
      
      if (response.data.success && response.data.data) {
        token.value = response.data.data.token
        user.value = response.data.data.user
        
        // 保存到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('user', JSON.stringify(user.value))
      } else {
        throw new Error(response.data.error || 'Login failed')
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Login failed')
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 获取用户资料
  const fetchProfile = async (): Promise<void> => {
    try {
      const response = await api.get<ApiResponse<User>>('/users/profile')
      if (response.data.success && response.data.data) {
        user.value = response.data.data
        localStorage.setItem('user', JSON.stringify(user.value))
      }
    } catch (error) {
      console.error('Failed to fetch profile:', error)
    }
  }

  // 更新用户资料
  const updateProfile = async (data: { email: string }): Promise<void> => {
    const response = await api.put<ApiResponse>('/users/profile', data)
    if (response.data.success) {
      await fetchProfile()
    } else {
      throw new Error(response.data.error || 'Update failed')
    }
  }

  // 修改密码
  const changePassword = async (data: { current_password: string; new_password: string }): Promise<void> => {
    const response = await api.put<ApiResponse>('/users/password', data)
    if (!response.data.success) {
      throw new Error(response.data.error || 'Password change failed')
    }
  }

  return {
    token,
    user,
    loading,
    isAuthenticated,
    isAdmin,
    initializeAuth,
    login,
    logout,
    fetchProfile,
    updateProfile,
    changePassword,
  }
})
