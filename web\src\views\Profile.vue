<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="max-w-2xl mx-auto">
        <div class="mb-6">
          <h1 class="text-2xl font-bold text-gray-900">个人资料</h1>
          <p class="mt-1 text-sm text-gray-600">管理您的账户信息和设置</p>
        </div>

        <!-- 基本信息 -->
        <div class="card mb-6">
          <div class="card-header">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              基本信息
            </h3>
          </div>
          <div class="card-body">
            <form @submit.prevent="updateProfile" class="space-y-6">
              <div>
                <label class="form-label">邮箱地址</label>
                <input
                  v-model="profileForm.email"
                  type="email"
                  required
                  class="form-input"
                />
              </div>

              <div>
                <label class="form-label">账户类型</label>
                <p class="mt-1 text-sm text-gray-900">
                  {{ authStore.user?.is_admin ? "管理员" : "普通用户" }}
                </p>
              </div>

              <div>
                <label class="form-label">注册时间</label>
                <p class="mt-1 text-sm text-gray-900">
                  {{ formatDate(authStore.user?.created_at || "") }}
                </p>
              </div>

              <div v-if="profileError" class="rounded-md bg-red-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">更新失败</h3>
                    <div class="mt-2 text-sm text-red-700">
                      {{ profileError }}
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="profileSuccess" class="rounded-md bg-green-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <CheckCircleIcon class="h-5 w-5 text-green-400" />
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">更新成功</h3>
                  </div>
                </div>
              </div>

              <div class="flex justify-end">
                <button
                  type="submit"
                  :disabled="profileLoading"
                  class="btn-primary disabled:opacity-50"
                >
                  <span v-if="profileLoading">更新中...</span>
                  <span v-else>更新资料</span>
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- 配额信息 -->
        <div class="card mb-6">
          <div class="card-header">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              配额信息
            </h3>
          </div>
          <div class="card-body">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
              <div>
                <label class="form-label">总配额</label>
                <p class="mt-1 text-2xl font-semibold text-gray-900">
                  {{ authStore.user?.max_license_quota || 0 }}
                </p>
              </div>
              <div>
                <label class="form-label">已使用</label>
                <p class="mt-1 text-2xl font-semibold text-blue-600">
                  {{ authStore.user?.used_license_quota || 0 }}
                </p>
              </div>
              <div>
                <label class="form-label">剩余</label>
                <p class="mt-1 text-2xl font-semibold text-green-600">
                  {{
                    (authStore.user?.max_license_quota || 0) -
                    (authStore.user?.used_license_quota || 0)
                  }}
                </p>
              </div>
            </div>

            <div class="mt-4">
              <div class="mb-2 flex justify-between text-sm">
                <span class="text-gray-600">使用进度</span>
                <span class="text-gray-600">{{ quotaPercentage }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: quotaPercentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 修改密码 -->
        <ChangePasswordForm @success="handlePasswordChangeSuccess" />
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import Layout from "@/components/Layout.vue";
import ChangePasswordForm from "@/components/ChangePasswordForm.vue";
import { useAuthStore } from "@/stores/auth";
import {
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from "@heroicons/vue/24/outline";

const authStore = useAuthStore();

const profileLoading = ref(false);
const profileError = ref("");
const profileSuccess = ref(false);

const profileForm = reactive({
  email: "",
});

const quotaPercentage = computed(() => {
  const user = authStore.user;
  if (!user || user.max_license_quota === 0) return 0;
  return Math.round((user.used_license_quota / user.max_license_quota) * 100);
});

const updateProfile = async () => {
  if (profileLoading.value) return;

  profileLoading.value = true;
  profileError.value = "";
  profileSuccess.value = false;

  try {
    await authStore.updateProfile(profileForm);
    profileSuccess.value = true;
    setTimeout(() => {
      profileSuccess.value = false;
    }, 3000);
  } catch (err: any) {
    profileError.value = err.message || "更新失败";
  } finally {
    profileLoading.value = false;
  }
};

const handlePasswordChangeSuccess = () => {
  // 密码修改成功后的处理逻辑
  console.log("密码修改成功");
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("zh-CN");
};

onMounted(() => {
  if (authStore.user) {
    profileForm.email = authStore.user.email;
  }
});
</script>
