package api

import (
	"licmanager/internal/api/handlers"
	"licmanager/internal/api/middleware"
	"licmanager/internal/config"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRouter(db *gorm.DB, cfg *config.Config) *gin.Engine {
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// 中间件
	router.Use(middleware.CORS())
	router.Use(middleware.Logger())

	// 静态文件服务 (前端构建文件)
	router.Static("/assets", "./web/dist/assets")
	router.StaticFile("/", "./web/dist/index.html")
	router.StaticFile("/favicon.ico", "./web/dist/favicon.ico")

	// 初始化处理器
	h := handlers.NewHandlers(db, cfg)

	// API路由组
	api := router.Group("/api")
	{
		// 公开路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", h.Login)
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware(cfg.JWTSecret))
		{
			// 用户相关
			users := protected.Group("/users")
			{
				users.GET("/profile", h.GetProfile)
				users.PUT("/profile", h.UpdateProfile)
				users.PUT("/password", h.ChangePassword)
			}

			// 产品相关
			products := protected.Group("/products")
			{
				products.GET("/", h.GetProducts)
				products.GET("/:id", h.GetProduct)
			}

			// 授权申请相关
			licenses := protected.Group("/licenses")
			{
				licenses.POST("/request", h.RequestLicense)
				licenses.GET("/", h.GetUserLicenses)
				licenses.GET("/:id", h.GetLicense)
			}

			// 统计相关
			stats := protected.Group("/stats")
			{
				stats.GET("/dashboard", h.GetDashboardStats)
			}

			// 管理员专用路由
			admin := protected.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				// 用户管理
				adminUsers := admin.Group("/users")
				{
					adminUsers.GET("/", h.GetAllUsers)
					adminUsers.POST("/", h.CreateUser)
					adminUsers.PUT("/:id", h.UpdateUser)
					adminUsers.PUT("/:id/quota", h.UpdateUserQuota)
					adminUsers.DELETE("/:id", h.DeleteUser)
				}

				// 产品管理
				adminProducts := admin.Group("/products")
				{
					adminProducts.POST("/", h.CreateProduct)
					adminProducts.PUT("/:id", h.UpdateProduct)
					adminProducts.DELETE("/:id", h.DeleteProduct)
				}

				// 授权管理
				adminLicenses := admin.Group("/licenses")
				{
					adminLicenses.GET("/", h.GetAllLicenses)
					adminLicenses.PUT("/:id/revoke", h.RevokeLicense)
				}

				// 日志管理
				adminLogs := admin.Group("/logs")
				{
					adminLogs.GET("/", h.GetAdminLogs)
				}

				// 统计分析
				adminStats := admin.Group("/stats")
				{
					adminStats.GET("/overview", h.GetAdminStats)
					adminStats.GET("/users", h.GetUserStats)
					adminStats.GET("/licenses", h.GetLicenseStats)
				}
			}
		}
	}

	// 前端路由回退
	router.NoRoute(func(c *gin.Context) {
		c.File("./web/dist/index.html")
	})

	return router
}
