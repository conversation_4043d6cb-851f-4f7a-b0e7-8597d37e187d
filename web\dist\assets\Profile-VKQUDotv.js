import{d as L,u as T,r as u,a as C,k as z,l as F,m as $,p as A,b as s,w as D,c as r,e as b,f as w,v as y,t as i,h as o,g,s as G,o as d}from"./index-DgD6jlCj.js";import{_ as H}from"./Layout.vue_vue_type_script_setup_true_lang-DaBFh0gi.js";import{r as M}from"./ExclamationTriangleIcon-BBsSUOYS.js";import{r as N}from"./CheckCircleIcon-CvQFFXss.js";const I={class:"px-4 py-6 sm:px-0"},J={class:"max-w-2xl mx-auto"},K={class:"card mb-6"},O={class:"card-body"},Q={class:"mt-1 text-sm text-gray-900"},R={class:"mt-1 text-sm text-gray-900"},W={key:0,class:"rounded-md bg-red-50 p-4"},X={class:"flex"},Y={class:"flex-shrink-0"},Z={class:"ml-3"},ss={class:"mt-2 text-sm text-red-700"},es={key:1,class:"rounded-md bg-green-50 p-4"},ts={class:"flex"},ls={class:"flex-shrink-0"},as={class:"flex justify-end"},os=["disabled"],ds={key:0},rs={key:1},is={class:"card mb-6"},ns={class:"card-body"},us={class:"grid grid-cols-1 gap-6 sm:grid-cols-3"},ms={class:"mt-1 text-2xl font-semibold text-gray-900"},cs={class:"mt-1 text-2xl font-semibold text-blue-600"},ps={class:"mt-1 text-2xl font-semibold text-green-600"},fs={class:"mt-4"},_s={class:"mb-2 flex justify-between text-sm"},vs={class:"text-gray-600"},xs={class:"w-full bg-gray-200 rounded-full h-2"},bs={class:"card"},ws={class:"card-body"},ys={key:0,class:"rounded-md bg-red-50 p-4"},gs={class:"flex"},hs={class:"flex-shrink-0"},ks={class:"ml-3"},qs={class:"mt-2 text-sm text-red-700"},Ss={key:1,class:"rounded-md bg-green-50 p-4"},Vs={class:"flex"},Ps={class:"flex-shrink-0"},Bs={class:"flex justify-end"},Cs=["disabled"],Ds={key:0},Ms={key:1},Ts=L({__name:"Profile",setup(Ns){const l=T(),m=u(!1),f=u(""),_=u(!1),c=u(!1),p=u(""),v=u(!1),x=C({email:""}),t=C({current_password:"",new_password:"",confirm_password:""}),h=z(()=>{const a=l.user;return!a||a.max_license_quota===0?0:Math.round(a.used_license_quota/a.max_license_quota*100)}),U=async()=>{if(!m.value){m.value=!0,f.value="",_.value=!1;try{await l.updateProfile(x),_.value=!0,setTimeout(()=>{_.value=!1},3e3)}catch(a){f.value=a.message||"更新失败"}finally{m.value=!1}}},j=async()=>{if(!c.value){if(t.new_password!==t.confirm_password){p.value="两次输入的密码不一致";return}c.value=!0,p.value="",v.value=!1;try{await l.changePassword({current_password:t.current_password,new_password:t.new_password}),v.value=!0,t.current_password="",t.new_password="",t.confirm_password="",setTimeout(()=>{v.value=!1},3e3)}catch(a){p.value=a.message||"密码修改失败"}finally{c.value=!1}}},E=a=>new Date(a).toLocaleString("zh-CN");return F(()=>{l.user&&(x.email=l.user.email)}),(a,e)=>(d(),$(H,null,{default:A(()=>{var k,q,S,V,P,B;return[s("div",I,[s("div",J,[e[22]||(e[22]=s("div",{class:"mb-6"},[s("h1",{class:"text-2xl font-bold text-gray-900"},"个人资料"),s("p",{class:"mt-1 text-sm text-gray-600"}," 管理您的账户信息和设置 ")],-1)),s("div",K,[e[9]||(e[9]=s("div",{class:"card-header"},[s("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"基本信息")],-1)),s("div",O,[s("form",{onSubmit:D(U,["prevent"]),class:"space-y-6"},[s("div",null,[e[4]||(e[4]=s("label",{class:"form-label"},"邮箱地址",-1)),w(s("input",{"onUpdate:modelValue":e[0]||(e[0]=n=>x.email=n),type:"email",required:"",class:"form-input"},null,512),[[y,x.email]])]),s("div",null,[e[5]||(e[5]=s("label",{class:"form-label"},"账户类型",-1)),s("p",Q,i((k=o(l).user)!=null&&k.is_admin?"管理员":"普通用户"),1)]),s("div",null,[e[6]||(e[6]=s("label",{class:"form-label"},"注册时间",-1)),s("p",R,i(E(((q=o(l).user)==null?void 0:q.created_at)||"")),1)]),f.value?(d(),r("div",W,[s("div",X,[s("div",Y,[g(o(M),{class:"h-5 w-5 text-red-400"})]),s("div",Z,[e[7]||(e[7]=s("h3",{class:"text-sm font-medium text-red-800"},"更新失败",-1)),s("div",ss,i(f.value),1)])])])):b("",!0),_.value?(d(),r("div",es,[s("div",ts,[s("div",ls,[g(o(N),{class:"h-5 w-5 text-green-400"})]),e[8]||(e[8]=s("div",{class:"ml-3"},[s("h3",{class:"text-sm font-medium text-green-800"},"更新成功")],-1))])])):b("",!0),s("div",as,[s("button",{type:"submit",disabled:m.value,class:"btn-primary disabled:opacity-50"},[m.value?(d(),r("span",ds,"更新中...")):(d(),r("span",rs,"更新资料"))],8,os)])],32)])]),s("div",is,[e[14]||(e[14]=s("div",{class:"card-header"},[s("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"配额信息")],-1)),s("div",ns,[s("div",us,[s("div",null,[e[10]||(e[10]=s("label",{class:"form-label"},"总配额",-1)),s("p",ms,i(((S=o(l).user)==null?void 0:S.max_license_quota)||0),1)]),s("div",null,[e[11]||(e[11]=s("label",{class:"form-label"},"已使用",-1)),s("p",cs,i(((V=o(l).user)==null?void 0:V.used_license_quota)||0),1)]),s("div",null,[e[12]||(e[12]=s("label",{class:"form-label"},"剩余",-1)),s("p",ps,i((((P=o(l).user)==null?void 0:P.max_license_quota)||0)-(((B=o(l).user)==null?void 0:B.used_license_quota)||0)),1)])]),s("div",fs,[s("div",_s,[e[13]||(e[13]=s("span",{class:"text-gray-600"},"使用进度",-1)),s("span",vs,i(h.value)+"%",1)]),s("div",xs,[s("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:G({width:h.value+"%"})},null,4)])])])]),s("div",bs,[e[21]||(e[21]=s("div",{class:"card-header"},[s("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"修改密码")],-1)),s("div",ws,[s("form",{onSubmit:D(j,["prevent"]),class:"space-y-6"},[s("div",null,[e[15]||(e[15]=s("label",{class:"form-label"},"当前密码",-1)),w(s("input",{"onUpdate:modelValue":e[1]||(e[1]=n=>t.current_password=n),type:"password",required:"",class:"form-input"},null,512),[[y,t.current_password]])]),s("div",null,[e[16]||(e[16]=s("label",{class:"form-label"},"新密码",-1)),w(s("input",{"onUpdate:modelValue":e[2]||(e[2]=n=>t.new_password=n),type:"password",required:"",minlength:"6",class:"form-input"},null,512),[[y,t.new_password]]),e[17]||(e[17]=s("p",{class:"mt-1 text-sm text-gray-500"},"密码长度至少6位",-1))]),s("div",null,[e[18]||(e[18]=s("label",{class:"form-label"},"确认新密码",-1)),w(s("input",{"onUpdate:modelValue":e[3]||(e[3]=n=>t.confirm_password=n),type:"password",required:"",class:"form-input"},null,512),[[y,t.confirm_password]])]),p.value?(d(),r("div",ys,[s("div",gs,[s("div",hs,[g(o(M),{class:"h-5 w-5 text-red-400"})]),s("div",ks,[e[19]||(e[19]=s("h3",{class:"text-sm font-medium text-red-800"},"修改失败",-1)),s("div",qs,i(p.value),1)])])])):b("",!0),v.value?(d(),r("div",Ss,[s("div",Vs,[s("div",Ps,[g(o(N),{class:"h-5 w-5 text-green-400"})]),e[20]||(e[20]=s("div",{class:"ml-3"},[s("h3",{class:"text-sm font-medium text-green-800"},"密码修改成功")],-1))])])):b("",!0),s("div",Bs,[s("button",{type:"submit",disabled:c.value||t.new_password!==t.confirm_password,class:"btn-primary disabled:opacity-50"},[c.value?(d(),r("span",Ds,"修改中...")):(d(),r("span",Ms,"修改密码"))],8,Cs)])],32)])])])])]}),_:1}))}});export{Ts as default};
