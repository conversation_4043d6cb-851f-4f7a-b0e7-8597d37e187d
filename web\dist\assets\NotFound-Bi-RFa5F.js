import{c as a,b as s,g as l,p as c,i as x,q as d,o as i}from"./index-DgD6jlCj.js";const m=(o,t)=>{const e=o.__vccOpts||o;for(const[n,r]of t)e[n]=r;return e},_={},f={class:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8"},p={class:"sm:mx-auto sm:w-full sm:max-w-md"},u={class:"text-center"},y={class:"mt-6"};function g(o,t){const e=d("router-link");return i(),a("div",f,[s("div",p,[s("div",u,[t[1]||(t[1]=s("h1",{class:"text-9xl font-bold text-primary-600"},"404",-1)),t[2]||(t[2]=s("h2",{class:"mt-4 text-3xl font-bold text-gray-900"},"页面未找到",-1)),t[3]||(t[3]=s("p",{class:"mt-2 text-base text-gray-500"}," 抱歉，您访问的页面不存在。 ",-1)),s("div",y,[l(e,{to:"/",class:"btn-primary"},{default:c(()=>t[0]||(t[0]=[x(" 返回首页 ")])),_:1,__:[0]})])])])])}const b=m(_,[["render",g]]);export{b as default};
