# License Manager - 授权码管理平台

一个功能完整的授权码申请与分发管理平台，支持用户自助申请授权码，管理员统一管理用户配额和产品信息。

## 功能特性

### 用户功能
- 🔐 用户登录认证
- 📊 个人仪表板，显示配额使用情况
- 🎫 申请产品授权码（基于机器码生成）
- 📝 查看授权申请历史记录
- 👤 个人资料管理
- 🔑 密码修改

### 管理员功能
- 👥 用户账户管理（创建、编辑、删除）
- 📈 用户配额管理（动态调整可申请授权数量）
- 🏷️ 产品分类管理
- 📋 授权记录管理（查看、撤销）
- 📊 统计分析（用户注册、授权分配趋势）
- 📜 操作日志审计

### 技术特性
- 🔒 JWT身份认证
- 🗄️ SQLite数据库
- 📱 响应式Web界面
- 🎨 TailwindCSS样式
- 📄 分页查询支持
- 🔍 数据验证和错误处理

## 技术栈

### 后端
- **Go 1.21+** - 主要编程语言
- **Gin** - Web框架
- **GORM** - ORM框架
- **SQLite** - 数据库
- **JWT** - 身份认证

### 前端
- **Vue 3** - 前端框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **TailwindCSS** - CSS框架
- **Pinia** - 状态管理
- **Vue Router** - 路由管理

## 快速开始

### 环境要求
- Go 1.21+
- Node.js 18+
- npm 或 yarn

### 安装依赖

#### 后端依赖
```bash
go mod tidy
```

#### 前端依赖
```bash
cd web
npm install
```

### 开发环境运行

#### 方式一：使用VSCode任务
1. 打开VSCode
2. 按 `Ctrl+Shift+P` 打开命令面板
3. 运行 "Tasks: Run Task"
4. 选择 "Start Full Application"

#### 方式二：手动启动

**启动后端服务：**
```bash
go run main.go
```

**启动前端开发服务器：**
```bash
cd web
npm run dev
```

### 生产环境部署

#### 构建前端
```bash
cd web
npm run build
```

#### 构建后端
```bash
go build -o bin/licmanager main.go
```

#### 运行
```bash
./bin/licmanager
```

## 配置说明

### 环境变量
- `PORT` - 服务端口（默认：8080）
- `DATABASE_PATH` - 数据库文件路径（默认：./data/licmanager.db）
- `JWT_SECRET` - JWT签名密钥（生产环境必须修改）
- `ENVIRONMENT` - 运行环境（development/production）

### 默认管理员账户
首次启动时会自动创建默认管理员账户：
- 邮箱：<EMAIL>
- 密码：随机生成（在控制台输出）

**⚠️ 请在首次登录后立即修改密码！**

## API文档

### 认证接口
- `POST /api/auth/login` - 用户登录

### 用户接口
- `GET /api/users/profile` - 获取用户资料
- `PUT /api/users/profile` - 更新用户资料
- `PUT /api/users/password` - 修改密码

### 产品接口
- `GET /api/products` - 获取产品列表
- `GET /api/products/:id` - 获取产品详情

### 授权接口
- `POST /api/licenses/request` - 申请授权码
- `GET /api/licenses` - 获取用户授权记录
- `GET /api/licenses/:id` - 获取授权详情

### 统计接口
- `GET /api/stats/dashboard` - 获取仪表板统计

### 管理员接口
- `GET /api/admin/users` - 获取所有用户
- `POST /api/admin/users` - 创建用户
- `PUT /api/admin/users/:id` - 更新用户
- `PUT /api/admin/users/:id/quota` - 更新用户配额
- `DELETE /api/admin/users/:id` - 删除用户
- `GET /api/admin/products` - 获取所有产品
- `POST /api/admin/products` - 创建产品
- `PUT /api/admin/products/:id` - 更新产品
- `DELETE /api/admin/products/:id` - 删除产品
- `GET /api/admin/licenses` - 获取所有授权记录
- `PUT /api/admin/licenses/:id/revoke` - 撤销授权
- `GET /api/admin/logs` - 获取操作日志
- `GET /api/admin/stats/overview` - 获取管理员统计概览

## 数据库结构

### 用户表 (users)
- id, email, password, is_admin, max_license_quota, used_license_quota, is_active

### 产品表 (products)
- id, name, description, category, is_active

### 授权申请表 (license_requests)
- id, user_id, product_id, machine_code, license_code, machine_name, company_name, contact_phone, os_type, install_time, remarks, status

### 管理员日志表 (admin_logs)
- id, admin_id, target_user_id, action, description, old_value, new_value, ip_address

## 授权码生成算法

授权码基于以下信息生成：
- 机器码（32位十六进制）
- 产品ID
- 用户ID
- 时间戳

使用SHA256哈希算法生成，格式为：XXXX-XXXX-XXXX-XXXX

## 开发指南

### 添加新功能
1. 后端：在 `internal/api/handlers/` 添加处理器
2. 前端：在 `web/src/views/` 添加页面组件
3. 更新路由配置
4. 添加相应的API接口

### 代码规范
- Go代码遵循官方格式化标准
- TypeScript/Vue代码使用ESLint规范
- 提交前请运行测试

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
