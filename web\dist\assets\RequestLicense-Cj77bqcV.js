import{d as F,r as c,a as O,l as T,m as B,p as w,n as k,b as e,w as D,c as d,e as S,f as n,z as V,y as M,F as N,v as u,g as v,h as y,t as f,i as h,q as $,j as L,o as r}from"./index-DgD6jlCj.js";import{_ as j}from"./Layout.vue_vue_type_script_setup_true_lang-DaBFh0gi.js";import{r as R}from"./ExclamationTriangleIcon-BBsSUOYS.js";import{r as z}from"./CheckCircleIcon-CvQFFXss.js";import{r as A}from"./ClipboardIcon-UcrMZxcD.js";const E={class:"px-4 py-6 sm:px-0"},I={class:"max-w-2xl mx-auto"},P={class:"card"},G={class:"card-body"},H=["value"],J={class:"border-t border-gray-200 pt-6"},K={class:"grid grid-cols-1 gap-6 sm:grid-cols-2"},Q={class:"sm:col-span-2"},X={class:"sm:col-span-2"},Y={key:0,class:"rounded-md bg-red-50 p-4"},Z={class:"flex"},ee={class:"flex-shrink-0"},te={class:"ml-3"},se={class:"mt-2 text-sm text-red-700"},oe={key:1,class:"rounded-md bg-green-50 p-4"},le={class:"flex"},ae={class:"flex-shrink-0"},ne={class:"ml-3"},re={class:"mt-2 text-sm text-green-700"},ie={class:"mt-2 flex items-center"},de={class:"bg-white px-2 py-1 rounded border font-mono"},ue={class:"flex justify-end space-x-3"},me=["disabled"],pe={key:0,class:"flex items-center"},ce={key:1},ge=F({__name:"RequestLicense",setup(ve){const U=L(),m=c(!1),p=c(""),x=c(!1),_=c([]),b=c(null),o=O({product_id:0,machine_code:"",machine_name:"",company_name:"",contact_phone:"",os_type:"",install_time:"",remarks:""}),C=async()=>{try{const l=await k.get("/products");l.data.success&&(_.value=l.data.data||[])}catch(l){console.error("Failed to fetch products:",l)}},W=async()=>{var l,t;if(!m.value){m.value=!0,p.value="",x.value=!1;try{const a={...o};a.install_time?a.install_time=new Date(a.install_time).toISOString():delete a.install_time;const i=await k.post("/licenses/request",a);i.data.success?(x.value=!0,b.value=i.data.data,setTimeout(()=>{U.push("/licenses")},3e3)):p.value=i.data.error||"申请失败"}catch(a){p.value=((t=(l=a.response)==null?void 0:l.data)==null?void 0:t.error)||"申请失败，请检查输入信息"}finally{m.value=!1}}},q=async l=>{try{await navigator.clipboard.writeText(l)}catch(t){console.error("Failed to copy:",t)}};return T(()=>{C()}),(l,t)=>{const a=$("router-link");return r(),B(j,null,{default:w(()=>{var i;return[e("div",E,[e("div",I,[t[26]||(t[26]=e("div",{class:"mb-6"},[e("h1",{class:"text-2xl font-bold text-gray-900"},"申请授权码"),e("p",{class:"mt-1 text-sm text-gray-600"}," 为您的产品申请新的授权码 ")],-1)),e("div",P,[e("div",G,[e("form",{onSubmit:D(W,["prevent"]),class:"space-y-6"},[e("div",null,[t[10]||(t[10]=e("label",{class:"form-label"},"选择产品 *",-1)),n(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>o.product_id=s),required:"",class:"form-input"},[t[9]||(t[9]=e("option",{value:""},"请选择产品",-1)),(r(!0),d(N,null,M(_.value,s=>(r(),d("option",{key:s.id,value:s.id},f(s.name)+" - "+f(s.category),9,H))),128))],512),[[V,o.product_id]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"form-label"},"机器码 *",-1)),n(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>o.machine_code=s),type:"text",required:"",placeholder:"请输入32位十六进制机器码",pattern:"[0-9A-Fa-f]{32}",maxlength:"32",class:"form-input font-mono"},null,512),[[u,o.machine_code]]),t[12]||(t[12]=e("p",{class:"mt-1 text-sm text-gray-500"}," 机器码应为32位十六进制字符串，由您的软件生成 ",-1))]),e("div",J,[t[20]||(t[20]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"可选信息",-1)),e("div",K,[e("div",null,[t[13]||(t[13]=e("label",{class:"form-label"},"机器名称",-1)),n(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>o.machine_name=s),type:"text",placeholder:"例如：办公室电脑",class:"form-input"},null,512),[[u,o.machine_name]])]),e("div",null,[t[14]||(t[14]=e("label",{class:"form-label"},"单位名称",-1)),n(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>o.company_name=s),type:"text",placeholder:"例如：某某公司",class:"form-input"},null,512),[[u,o.company_name]])]),e("div",null,[t[15]||(t[15]=e("label",{class:"form-label"},"联系电话",-1)),n(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>o.contact_phone=s),type:"tel",placeholder:"例如：13800138000",class:"form-input"},null,512),[[u,o.contact_phone]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"form-label"},"操作系统",-1)),n(e("select",{"onUpdate:modelValue":t[5]||(t[5]=s=>o.os_type=s),class:"form-input"},t[16]||(t[16]=[e("option",{value:""},"请选择",-1),e("option",{value:"Windows 11"},"Windows 11",-1),e("option",{value:"Windows 10"},"Windows 10",-1),e("option",{value:"Windows Server 2022"},"Windows Server 2022",-1),e("option",{value:"Windows Server 2019"},"Windows Server 2019",-1),e("option",{value:"macOS"},"macOS",-1),e("option",{value:"Ubuntu"},"Ubuntu",-1),e("option",{value:"CentOS"},"CentOS",-1),e("option",{value:"其他"},"其他",-1)]),512),[[V,o.os_type]])]),e("div",Q,[t[18]||(t[18]=e("label",{class:"form-label"},"安装时间",-1)),n(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>o.install_time=s),type:"datetime-local",class:"form-input"},null,512),[[u,o.install_time]])]),e("div",X,[t[19]||(t[19]=e("label",{class:"form-label"},"备注信息",-1)),n(e("textarea",{"onUpdate:modelValue":t[7]||(t[7]=s=>o.remarks=s),rows:"3",placeholder:"其他需要说明的信息...",class:"form-input"},null,512),[[u,o.remarks]])])])]),p.value?(r(),d("div",Y,[e("div",Z,[e("div",ee,[v(y(R),{class:"h-5 w-5 text-red-400"})]),e("div",te,[t[21]||(t[21]=e("h3",{class:"text-sm font-medium text-red-800"}," 申请失败 ",-1)),e("div",se,f(p.value),1)])])])):S("",!0),x.value?(r(),d("div",oe,[e("div",le,[e("div",ae,[v(y(z),{class:"h-5 w-5 text-green-400"})]),e("div",ne,[t[23]||(t[23]=e("h3",{class:"text-sm font-medium text-green-800"}," 申请成功 ",-1)),e("div",re,[t[22]||(t[22]=e("p",null,"您的授权码已生成：",-1)),e("div",ie,[e("code",de,f((i=b.value)==null?void 0:i.license_code),1),e("button",{onClick:t[8]||(t[8]=s=>{var g;return q(((g=b.value)==null?void 0:g.license_code)||"")}),class:"ml-2 text-green-600 hover:text-green-800",title:"复制授权码"},[v(y(A),{class:"h-4 w-4"})])])])])])])):S("",!0),e("div",ue,[v(a,{to:"/licenses",class:"btn-secondary"},{default:w(()=>t[24]||(t[24]=[h(" 返回列表 ")])),_:1,__:[24]}),e("button",{type:"submit",disabled:m.value,class:"btn-primary disabled:opacity-50"},[m.value?(r(),d("span",pe,t[25]||(t[25]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),h(" 申请中... ")]))):(r(),d("span",ce,"申请授权码"))],8,me)])],32)])])])])]}),_:1})}}});export{ge as default};
