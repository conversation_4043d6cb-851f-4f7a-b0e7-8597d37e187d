import{d as v,u as b,r as i,a as g,c as l,b as e,w,e as u,f as m,v as p,g as h,h as _,t as c,i as k,j as V,o as n}from"./index-DgD6jlCj.js";import{r as S}from"./ExclamationTriangleIcon-BBsSUOYS.js";const N={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},j={class:"max-w-md w-full space-y-8"},B={class:"rounded-md shadow-sm -space-y-px"},q={key:0,class:"rounded-md bg-red-50 p-4"},z={class:"flex"},C={class:"flex-shrink-0"},D={class:"ml-3"},M={class:"mt-2 text-sm text-red-700"},T=["disabled"],U={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},R=v({__name:"Login",setup(A){const f=V(),y=b(),t=i(!1),o=i(""),r=g({email:"",password:""}),x=async()=>{if(!t.value){t.value=!0,o.value="";try{await y.login(r),f.push("/")}catch(d){o.value=d.message||"登录失败，请检查您的凭据"}finally{t.value=!1}}};return(d,s)=>(n(),l("div",N,[e("div",j,[s[6]||(s[6]=e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," 登录到授权管理系统 "),e("p",{class:"mt-2 text-center text-sm text-gray-600"}," 请使用您的账户凭据登录 ")],-1)),e("form",{class:"mt-8 space-y-6",onSubmit:w(x,["prevent"])},[e("div",B,[e("div",null,[s[2]||(s[2]=e("label",{for:"email",class:"sr-only"},"邮箱地址",-1)),m(e("input",{id:"email","onUpdate:modelValue":s[0]||(s[0]=a=>r.email=a),name:"email",type:"email",autocomplete:"email",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"邮箱地址"},null,512),[[p,r.email]])]),e("div",null,[s[3]||(s[3]=e("label",{for:"password",class:"sr-only"},"密码",-1)),m(e("input",{id:"password","onUpdate:modelValue":s[1]||(s[1]=a=>r.password=a),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"密码"},null,512),[[p,r.password]])])]),o.value?(n(),l("div",q,[e("div",z,[e("div",C,[h(_(S),{class:"h-5 w-5 text-red-400"})]),e("div",D,[s[4]||(s[4]=e("h3",{class:"text-sm font-medium text-red-800"}," 登录失败 ",-1)),e("div",M,c(o.value),1)])])])):u("",!0),e("div",null,[e("button",{type:"submit",disabled:t.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[t.value?(n(),l("span",U,s[5]||(s[5]=[e("div",{class:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"},null,-1)]))):u("",!0),k(" "+c(t.value?"登录中...":"登录"),1)],8,T)])],32)])]))}});export{R as default};
