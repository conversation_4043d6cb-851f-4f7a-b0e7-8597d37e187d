<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 sm:mx-0 sm:h-10 sm:w-10">
              <KeyIcon class="h-6 w-6 text-primary-600" />
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                授权详情
              </h3>
              <div class="mt-4 space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">产品信息</label>
                  <div class="mt-1">
                    <p class="text-sm text-gray-900">{{ license.product?.name }}</p>
                    <p class="text-sm text-gray-500">{{ license.product?.description }}</p>
                    <p class="text-sm text-gray-500">分类：{{ license.product?.category }}</p>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">授权码</label>
                  <div class="mt-1 flex items-center">
                    <code class="flex-1 text-sm font-mono bg-gray-100 px-3 py-2 rounded border">
                      {{ license.license_code }}
                    </code>
                    <button
                      @click="copyToClipboard(license.license_code)"
                      class="ml-2 p-2 text-gray-400 hover:text-gray-600 rounded"
                      title="复制授权码"
                    >
                      <ClipboardIcon class="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">机器码</label>
                  <div class="mt-1">
                    <code class="block text-sm font-mono bg-gray-100 px-3 py-2 rounded border">
                      {{ formatMachineCode(license.machine_code) }}
                    </code>
                  </div>
                </div>

                <div v-if="license.machine_name">
                  <label class="block text-sm font-medium text-gray-700">机器名称</label>
                  <p class="mt-1 text-sm text-gray-900">{{ license.machine_name }}</p>
                </div>

                <div v-if="license.company_name">
                  <label class="block text-sm font-medium text-gray-700">单位名称</label>
                  <p class="mt-1 text-sm text-gray-900">{{ license.company_name }}</p>
                </div>

                <div v-if="license.contact_phone">
                  <label class="block text-sm font-medium text-gray-700">联系电话</label>
                  <p class="mt-1 text-sm text-gray-900">{{ license.contact_phone }}</p>
                </div>

                <div v-if="license.os_type">
                  <label class="block text-sm font-medium text-gray-700">操作系统</label>
                  <p class="mt-1 text-sm text-gray-900">{{ license.os_type }}</p>
                </div>

                <div v-if="license.install_time">
                  <label class="block text-sm font-medium text-gray-700">安装时间</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDate(license.install_time) }}</p>
                </div>

                <div v-if="license.remarks">
                  <label class="block text-sm font-medium text-gray-700">备注信息</label>
                  <p class="mt-1 text-sm text-gray-900">{{ license.remarks }}</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">状态</label>
                  <span
                    :class="[
                      'inline-flex mt-1 px-2 py-1 text-xs font-semibold rounded-full',
                      license.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ license.status === 'active' ? '有效' : '已撤销' }}
                  </span>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">申请时间</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDate(license.created_at) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="$emit('close')"
            class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { LicenseRequest } from '@/types'
import { KeyIcon, ClipboardIcon } from '@heroicons/vue/24/outline'

defineProps<{
  license: LicenseRequest
}>()

defineEmits<{
  close: []
}>()

const formatMachineCode = (code: string) => {
  return code.replace(/(.{8})/g, '$1-').slice(0, -1)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // 这里可以添加一个toast提示
  } catch (error) {
    console.error('Failed to copy:', error)
  }
}
</script>
