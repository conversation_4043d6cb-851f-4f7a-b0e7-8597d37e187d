<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
              <PencilIcon class="h-6 w-6 text-blue-600" />
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                编辑用户信息
              </h3>
              <div class="mt-4">
                <form @submit.prevent="handleSubmit" class="space-y-4">
                  <div>
                    <label class="form-label">邮箱地址 *</label>
                    <input
                      v-model="form.email"
                      type="email"
                      required
                      class="form-input"
                      placeholder="请输入邮箱地址"
                    />
                  </div>

                  <div class="flex items-center">
                    <input
                      v-model="form.is_admin"
                      type="checkbox"
                      class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label class="ml-2 block text-sm text-gray-900">
                      管理员权限
                    </label>
                  </div>

                  <div class="flex items-center">
                    <input
                      v-model="form.is_active"
                      type="checkbox"
                      class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label class="ml-2 block text-sm text-gray-900">
                      账户状态（启用）
                    </label>
                  </div>

                  <div class="bg-gray-50 p-3 rounded-md">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">当前配额信息</h4>
                    <div class="text-sm text-gray-600 space-y-1">
                      <p>最大配额：{{ user.max_license_quota }}</p>
                      <p>已使用：{{ user.used_license_quota }}</p>
                      <p>剩余：{{ user.max_license_quota - user.used_license_quota }}</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                      配额管理请使用"配额"按钮进行操作
                    </p>
                  </div>

                  <div v-if="error" class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">更新失败</h3>
                        <div class="mt-2 text-sm text-red-700">{{ error }}</div>
                      </div>
                    </div>
                  </div>

                  <div v-if="success" class="rounded-md bg-green-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <CheckCircleIcon class="h-5 w-5 text-green-400" />
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">更新成功</h3>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="handleSubmit"
            :disabled="loading"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
          >
            <span v-if="loading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              更新中...
            </span>
            <span v-else>更新用户</span>
          </button>
          <button
            @click="$emit('close')"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import api from '@/utils/api'
import type { User, UpdateUserRequest, ApiResponse } from '@/types'
import { 
  PencilIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon 
} from '@heroicons/vue/24/outline'

interface Props {
  user: User
}

const props = defineProps<Props>()

defineEmits<{
  close: []
  success: []
}>()

const loading = ref(false)
const error = ref('')
const success = ref(false)

const form = reactive<UpdateUserRequest>({
  email: '',
  is_admin: false,
  is_active: true
})

const handleSubmit = async () => {
  if (loading.value) return
  
  loading.value = true
  error.value = ''
  success.value = false
  
  try {
    const response = await api.put<ApiResponse>(`/admin/users/${props.user.id}`, form)
    
    if (response.data.success) {
      success.value = true
      
      // 1秒后触发成功事件
      setTimeout(() => {
        // $emit('success')
      }, 1000)
    } else {
      error.value = response.data.error || '更新失败'
    }
  } catch (err: any) {
    error.value = err.response?.data?.error || '更新失败，请检查输入信息'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  form.email = props.user.email
  form.is_admin = props.user.is_admin
  form.is_active = props.user.is_active
})
</script>
