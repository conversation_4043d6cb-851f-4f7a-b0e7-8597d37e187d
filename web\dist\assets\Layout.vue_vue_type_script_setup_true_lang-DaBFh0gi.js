import{A as be,B as W,C as D,D as O,k as P,r as _,l as ue,E as ye,G as he,F as xe,H as se,d as F,I as we,c as B,b as I,o as U,u as Ie,g,e as Se,p as b,q as _e,h as w,J as Ee,i as k,T as Q,x as M,t as ke,j as Pe}from"./index-DgD6jlCj.js";var Y;let Me=Symbol("headlessui.useid"),Te=0;const q=(Y=be)!=null?Y:function(){return W(Me,()=>`${++Te}`)()};function h(e){var t;if(e==null||e.value==null)return null;let l=(t=e.value.$el)!=null?t:e.value;return l instanceof Node?l:null}function R(e,t,...l){if(e in t){let s=t[e];return typeof s=="function"?s(...l):s}let a=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(s=>`"${s}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,R),a}var De=Object.defineProperty,Ae=(e,t,l)=>t in e?De(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Z=(e,t,l)=>(Ae(e,typeof t!="symbol"?t+"":t,l),l);let Oe=class{constructor(){Z(this,"current",this.detect()),Z(this,"currentId",0)}set(t){this.current!==t&&(this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}},G=new Oe;function X(e){if(G.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(e!=null&&e.hasOwnProperty("value")){let t=h(e);if(t)return t.ownerDocument}return document}let H=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var K=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(K||{}),Fe=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(Fe||{}),$e=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))($e||{});function ie(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(H)).sort((t,l)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(l.tabIndex||Number.MAX_SAFE_INTEGER)))}var z=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(z||{});function J(e,t=0){var l;return e===((l=X(e))==null?void 0:l.body)?!1:R(t,{0(){return e.matches(H)},1(){let a=e;for(;a!==null;){if(a.matches(H))return!0;a=a.parentElement}return!1}})}function ce(e){let t=X(e);D(()=>{t&&!J(t.activeElement,0)&&Re(e)})}var Ne=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Ne||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function Re(e){e==null||e.focus({preventScroll:!0})}let Le=["textarea","input"].join(",");function Ce(e){var t,l;return(l=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,Le))!=null?l:!1}function de(e,t=l=>l){return e.slice().sort((l,a)=>{let s=t(l),n=t(a);if(s===null||n===null)return 0;let r=s.compareDocumentPosition(n);return r&Node.DOCUMENT_POSITION_FOLLOWING?-1:r&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function je(e,t){return Be(ie(),t,{relativeTo:e})}function Be(e,t,{sorted:l=!0,relativeTo:a=null,skipElements:s=[]}={}){var n;let r=(n=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e==null?void 0:e.ownerDocument)!=null?n:document,o=Array.isArray(e)?l?de(e):e:ie(e);s.length>0&&o.length>1&&(o=o.filter(i=>!s.includes(i))),a=a??r.activeElement;let d=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),v=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,o.indexOf(a))-1;if(t&4)return Math.max(0,o.indexOf(a))+1;if(t&8)return o.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),m=t&32?{preventScroll:!0}:{},p=0,u=o.length,c;do{if(p>=u||p+u<=0)return 0;let i=v+p;if(t&16)i=(i+u)%u;else{if(i<0)return 3;if(i>=u)return 1}c=o[i],c==null||c.focus(m),p+=d}while(c!==r.activeElement);return t&6&&Ce(c)&&c.select(),2}function Ue(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function He(){return/Android/gi.test(window.navigator.userAgent)}function Ke(){return Ue()||He()}function N(e,t,l){G.isServer||O(a=>{document.addEventListener(e,t,l),a(()=>document.removeEventListener(e,t,l))})}function Ve(e,t,l){G.isServer||O(a=>{window.addEventListener(e,t,l),a(()=>window.removeEventListener(e,t,l))})}function We(e,t,l=P(()=>!0)){function a(n,r){if(!l.value||n.defaultPrevented)return;let o=r(n);if(o===null||!o.getRootNode().contains(o))return;let d=function v(m){return typeof m=="function"?v(m()):Array.isArray(m)||m instanceof Set?m:[m]}(e);for(let v of d){if(v===null)continue;let m=v instanceof HTMLElement?v:h(v);if(m!=null&&m.contains(o)||n.composed&&n.composedPath().includes(m))return}return!J(o,z.Loose)&&o.tabIndex!==-1&&n.preventDefault(),t(n,o)}let s=_(null);N("pointerdown",n=>{var r,o;l.value&&(s.value=((o=(r=n.composedPath)==null?void 0:r.call(n))==null?void 0:o[0])||n.target)},!0),N("mousedown",n=>{var r,o;l.value&&(s.value=((o=(r=n.composedPath)==null?void 0:r.call(n))==null?void 0:o[0])||n.target)},!0),N("click",n=>{Ke()||s.value&&(a(n,()=>s.value),s.value=null)},!0),N("touchend",n=>a(n,()=>n.target instanceof HTMLElement?n.target:null),!0),Ve("blur",n=>a(n,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function ee(e,t){if(e)return e;let l=t??"button";if(typeof l=="string"&&l.toLowerCase()==="button")return"button"}function qe(e,t){let l=_(ee(e.value.type,e.value.as));return ue(()=>{l.value=ee(e.value.type,e.value.as)}),O(()=>{var a;l.value||h(t)&&h(t)instanceof HTMLButtonElement&&!((a=h(t))!=null&&a.hasAttribute("type"))&&(l.value="button")}),l}function te(e){return[e.screenX,e.screenY]}function Ge(){let e=_([-1,-1]);return{wasMoved(t){let l=te(t);return e.value[0]===l[0]&&e.value[1]===l[1]?!1:(e.value=l,!0)},update(t){e.value=te(t)}}}function Xe({container:e,accept:t,walk:l,enabled:a}){O(()=>{let s=e.value;if(!s||a!==void 0&&!a.value)return;let n=X(e);if(!n)return;let r=Object.assign(d=>t(d),{acceptNode:t}),o=n.createTreeWalker(s,NodeFilter.SHOW_ELEMENT,r,!1);for(;o.nextNode();)l(o.currentNode)})}var V=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(V||{}),ze=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(ze||{});function L({visible:e=!0,features:t=0,ourProps:l,theirProps:a,...s}){var n;let r=pe(a,l),o=Object.assign(s,{props:r});if(e||t&2&&r.static)return j(o);if(t&1){let d=(n=r.unmount)==null||n?0:1;return R(d,{0(){return null},1(){return j({...s,props:{...r,hidden:!0,style:{display:"none"}}})}})}return j(o)}function j({props:e,attrs:t,slots:l,slot:a,name:s}){var n,r;let{as:o,...d}=Je(e,["unmount","static"]),v=(n=l.default)==null?void 0:n.call(l,a),m={};if(a){let p=!1,u=[];for(let[c,i]of Object.entries(a))typeof i=="boolean"&&(p=!0),i===!0&&u.push(c);p&&(m["data-headlessui-state"]=u.join(" "))}if(o==="template"){if(v=fe(v??[]),Object.keys(d).length>0||Object.keys(t).length>0){let[p,...u]=v??[];if(!Qe(p)||u.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${s} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(d).concat(Object.keys(t)).map(f=>f.trim()).filter((f,x,y)=>y.indexOf(f)===x).sort((f,x)=>f.localeCompare(x)).map(f=>`  - ${f}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(f=>`  - ${f}`).join(`
`)].join(`
`));let c=pe((r=p.props)!=null?r:{},d,m),i=ye(p,c,!0);for(let f in c)f.startsWith("on")&&(i.props||(i.props={}),i.props[f]=c[f]);return i}return Array.isArray(v)&&v.length===1?v[0]:v}return he(o,Object.assign({},d,m),{default:()=>v})}function fe(e){return e.flatMap(t=>t.type===xe?fe(t.children):[t])}function pe(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},l={};for(let a of e)for(let s in a)s.startsWith("on")&&typeof a[s]=="function"?(l[s]!=null||(l[s]=[]),l[s].push(a[s])):t[s]=a[s];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(l).map(a=>[a,void 0])));for(let a in l)Object.assign(t,{[a](s,...n){let r=l[a];for(let o of r){if(s instanceof Event&&s.defaultPrevented)return;o(s,...n)}}});return t}function Je(e,t=[]){let l=Object.assign({},e);for(let a of t)a in l&&delete l[a];return l}function Qe(e){return e==null?!1:typeof e.type=="string"||typeof e.type=="object"||typeof e.type=="function"}let ve=Symbol("Context");var A=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(A||{});function Ye(){return W(ve,null)}function Ze(e){se(ve,e)}var S=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(S||{});function et(e){throw new Error("Unexpected object: "+e)}var E=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(E||{});function tt(e,t){let l=t.resolveItems();if(l.length<=0)return null;let a=t.resolveActiveIndex(),s=a??-1;switch(e.focus){case 0:{for(let n=0;n<l.length;++n)if(!t.resolveDisabled(l[n],n,l))return n;return a}case 1:{s===-1&&(s=l.length);for(let n=s-1;n>=0;--n)if(!t.resolveDisabled(l[n],n,l))return n;return a}case 2:{for(let n=s+1;n<l.length;++n)if(!t.resolveDisabled(l[n],n,l))return n;return a}case 3:{for(let n=l.length-1;n>=0;--n)if(!t.resolveDisabled(l[n],n,l))return n;return a}case 4:{for(let n=0;n<l.length;++n)if(t.resolveId(l[n],n,l)===e.id)return n;return a}case 5:return null;default:et(e)}}let ne=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function re(e){var t,l;let a=(t=e.innerText)!=null?t:"",s=e.cloneNode(!0);if(!(s instanceof HTMLElement))return a;let n=!1;for(let o of s.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))o.remove(),n=!0;let r=n?(l=s.innerText)!=null?l:"":a;return ne.test(r)&&(r=r.replace(ne,"")),r}function nt(e){let t=e.getAttribute("aria-label");if(typeof t=="string")return t.trim();let l=e.getAttribute("aria-labelledby");if(l){let a=l.split(" ").map(s=>{let n=document.getElementById(s);if(n){let r=n.getAttribute("aria-label");return typeof r=="string"?r.trim():re(n).trim()}return null}).filter(Boolean);if(a.length>0)return a.join(", ")}return re(e).trim()}function rt(e){let t=_(""),l=_("");return()=>{let a=h(e);if(!a)return"";let s=a.innerText;if(t.value===s)return l.value;let n=nt(a).trim().toLowerCase();return t.value=s,l.value=n,n}}var lt=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(lt||{}),at=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(at||{});function ot(e){requestAnimationFrame(()=>requestAnimationFrame(e))}let me=Symbol("MenuContext");function C(e){let t=W(me,null);if(t===null){let l=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,C),l}return t}let le=F({name:"Menu",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:l}){let a=_(1),s=_(null),n=_(null),r=_([]),o=_(""),d=_(null),v=_(1);function m(u=c=>c){let c=d.value!==null?r.value[d.value]:null,i=de(u(r.value.slice()),x=>h(x.dataRef.domRef)),f=c?i.indexOf(c):null;return f===-1&&(f=null),{items:i,activeItemIndex:f}}let p={menuState:a,buttonRef:s,itemsRef:n,items:r,searchQuery:o,activeItemIndex:d,activationTrigger:v,closeMenu:()=>{a.value=1,d.value=null},openMenu:()=>a.value=0,goToItem(u,c,i){let f=m(),x=tt(u===E.Specific?{focus:E.Specific,id:c}:{focus:u},{resolveItems:()=>f.items,resolveActiveIndex:()=>f.activeItemIndex,resolveId:y=>y.id,resolveDisabled:y=>y.dataRef.disabled});o.value="",d.value=x,v.value=i??1,r.value=f.items},search(u){let c=o.value!==""?0:1;o.value+=u.toLowerCase();let i=(d.value!==null?r.value.slice(d.value+c).concat(r.value.slice(0,d.value+c)):r.value).find(x=>x.dataRef.textValue.startsWith(o.value)&&!x.dataRef.disabled),f=i?r.value.indexOf(i):-1;f===-1||f===d.value||(d.value=f,v.value=1)},clearSearch(){o.value=""},registerItem(u,c){let i=m(f=>[...f,{id:u,dataRef:c}]);r.value=i.items,d.value=i.activeItemIndex,v.value=1},unregisterItem(u){let c=m(i=>{let f=i.findIndex(x=>x.id===u);return f!==-1&&i.splice(f,1),i});r.value=c.items,d.value=c.activeItemIndex,v.value=1}};return We([s,n],(u,c)=>{var i;p.closeMenu(),J(c,z.Loose)||(u.preventDefault(),(i=h(s))==null||i.focus())},P(()=>a.value===0)),se(me,p),Ze(P(()=>R(a.value,{0:A.Open,1:A.Closed}))),()=>{let u={open:a.value===0,close:p.closeMenu};return L({ourProps:{},theirProps:e,slot:u,slots:t,attrs:l,name:"Menu"})}}}),ae=F({name:"MenuButton",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:a}){var s;let n=(s=e.id)!=null?s:`headlessui-menu-button-${q()}`,r=C("MenuButton");a({el:r.buttonRef,$el:r.buttonRef});function o(p){switch(p.key){case S.Space:case S.Enter:case S.ArrowDown:p.preventDefault(),p.stopPropagation(),r.openMenu(),D(()=>{var u;(u=h(r.itemsRef))==null||u.focus({preventScroll:!0}),r.goToItem(E.First)});break;case S.ArrowUp:p.preventDefault(),p.stopPropagation(),r.openMenu(),D(()=>{var u;(u=h(r.itemsRef))==null||u.focus({preventScroll:!0}),r.goToItem(E.Last)});break}}function d(p){switch(p.key){case S.Space:p.preventDefault();break}}function v(p){e.disabled||(r.menuState.value===0?(r.closeMenu(),D(()=>{var u;return(u=h(r.buttonRef))==null?void 0:u.focus({preventScroll:!0})})):(p.preventDefault(),r.openMenu(),ot(()=>{var u;return(u=h(r.itemsRef))==null?void 0:u.focus({preventScroll:!0})})))}let m=qe(P(()=>({as:e.as,type:t.type})),r.buttonRef);return()=>{var p;let u={open:r.menuState.value===0},{...c}=e,i={ref:r.buttonRef,id:n,type:m.value,"aria-haspopup":"menu","aria-controls":(p=h(r.itemsRef))==null?void 0:p.id,"aria-expanded":r.menuState.value===0,onKeydown:o,onKeyup:d,onClick:v};return L({ourProps:i,theirProps:c,slot:u,attrs:t,slots:l,name:"MenuButton"})}}}),oe=F({name:"MenuItems",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:a}){var s;let n=(s=e.id)!=null?s:`headlessui-menu-items-${q()}`,r=C("MenuItems"),o=_(null);a({el:r.itemsRef,$el:r.itemsRef}),Xe({container:P(()=>h(r.itemsRef)),enabled:P(()=>r.menuState.value===0),accept(u){return u.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:u.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(u){u.setAttribute("role","none")}});function d(u){var c;switch(o.value&&clearTimeout(o.value),u.key){case S.Space:if(r.searchQuery.value!=="")return u.preventDefault(),u.stopPropagation(),r.search(u.key);case S.Enter:if(u.preventDefault(),u.stopPropagation(),r.activeItemIndex.value!==null){let i=r.items.value[r.activeItemIndex.value];(c=h(i.dataRef.domRef))==null||c.click()}r.closeMenu(),ce(h(r.buttonRef));break;case S.ArrowDown:return u.preventDefault(),u.stopPropagation(),r.goToItem(E.Next);case S.ArrowUp:return u.preventDefault(),u.stopPropagation(),r.goToItem(E.Previous);case S.Home:case S.PageUp:return u.preventDefault(),u.stopPropagation(),r.goToItem(E.First);case S.End:case S.PageDown:return u.preventDefault(),u.stopPropagation(),r.goToItem(E.Last);case S.Escape:u.preventDefault(),u.stopPropagation(),r.closeMenu(),D(()=>{var i;return(i=h(r.buttonRef))==null?void 0:i.focus({preventScroll:!0})});break;case S.Tab:u.preventDefault(),u.stopPropagation(),r.closeMenu(),D(()=>je(h(r.buttonRef),u.shiftKey?K.Previous:K.Next));break;default:u.key.length===1&&(r.search(u.key),o.value=setTimeout(()=>r.clearSearch(),350));break}}function v(u){switch(u.key){case S.Space:u.preventDefault();break}}let m=Ye(),p=P(()=>m!==null?(m.value&A.Open)===A.Open:r.menuState.value===0);return()=>{var u,c;let i={open:r.menuState.value===0},{...f}=e,x={"aria-activedescendant":r.activeItemIndex.value===null||(u=r.items.value[r.activeItemIndex.value])==null?void 0:u.id,"aria-labelledby":(c=h(r.buttonRef))==null?void 0:c.id,id:n,onKeydown:d,onKeyup:v,role:"menu",tabIndex:0,ref:r.itemsRef};return L({ourProps:x,theirProps:f,slot:i,attrs:t,slots:l,features:V.RenderStrategy|V.Static,visible:p.value,name:"MenuItems"})}}}),T=F({name:"MenuItem",inheritAttrs:!1,props:{as:{type:[Object,String],default:"template"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:l,expose:a}){var s;let n=(s=e.id)!=null?s:`headlessui-menu-item-${q()}`,r=C("MenuItem"),o=_(null);a({el:o,$el:o});let d=P(()=>r.activeItemIndex.value!==null?r.items.value[r.activeItemIndex.value].id===n:!1),v=rt(o),m=P(()=>({disabled:e.disabled,get textValue(){return v()},domRef:o}));ue(()=>r.registerItem(n,m)),we(()=>r.unregisterItem(n)),O(()=>{r.menuState.value===0&&d.value&&r.activationTrigger.value!==0&&D(()=>{var y,$;return($=(y=h(o))==null?void 0:y.scrollIntoView)==null?void 0:$.call(y,{block:"nearest"})})});function p(y){if(e.disabled)return y.preventDefault();r.closeMenu(),ce(h(r.buttonRef))}function u(){if(e.disabled)return r.goToItem(E.Nothing);r.goToItem(E.Specific,n)}let c=Ge();function i(y){c.update(y)}function f(y){c.wasMoved(y)&&(e.disabled||d.value||r.goToItem(E.Specific,n,0))}function x(y){c.wasMoved(y)&&(e.disabled||d.value&&r.goToItem(E.Nothing))}return()=>{let{disabled:y,...$}=e,ge={active:d.value,disabled:y,close:r.closeMenu};return L({ourProps:{id:n,ref:o,role:"menuitem",tabIndex:y===!0?void 0:-1,"aria-disabled":y===!0?!0:void 0,onClick:p,onFocus:u,onPointerenter:i,onMouseenter:i,onPointermove:f,onMousemove:f,onPointerleave:x,onMouseleave:x},theirProps:{...l,...$},slot:ge,attrs:l,slots:t,name:"MenuItem"})}}});function ut(e,t){return U(),B("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[I("path",{"fill-rule":"evenodd",d:"M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z","clip-rule":"evenodd"})])}const st={class:"min-h-screen bg-gray-50"},it={class:"bg-white shadow"},ct={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},dt={class:"flex justify-between h-16"},ft={class:"flex"},pt={class:"hidden sm:ml-6 sm:flex sm:space-x-8"},vt={key:0,class:"relative"},mt={class:"py-1"},gt={class:"hidden sm:ml-6 sm:flex sm:items-center"},bt={class:"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center"},yt={class:"text-white text-sm font-medium"},ht={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},It=F({__name:"Layout",setup(e){const t=Ie(),l=Pe(),a=()=>{t.logout(),l.push("/login")};return(s,n)=>{const r=_e("router-link");return U(),B("div",st,[I("nav",it,[I("div",ct,[I("div",dt,[I("div",ft,[n[10]||(n[10]=I("div",{class:"flex-shrink-0 flex items-center"},[I("h1",{class:"text-xl font-bold text-gray-900"},"License Manager")],-1)),I("div",pt,[g(r,{to:"/",class:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm","active-class":"border-primary-500 text-gray-900"},{default:b(()=>n[0]||(n[0]=[k(" 仪表板 ")])),_:1,__:[0]}),g(r,{to:"/licenses",class:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm","active-class":"border-primary-500 text-gray-900"},{default:b(()=>n[1]||(n[1]=[k(" 我的授权 ")])),_:1,__:[1]}),g(r,{to:"/licenses/request",class:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm","active-class":"border-primary-500 text-gray-900"},{default:b(()=>n[2]||(n[2]=[k(" 申请授权 ")])),_:1,__:[2]}),w(t).isAdmin?(U(),B("div",vt,[g(w(le),{as:"div",class:"relative inline-block text-left"},{default:b(()=>[I("div",null,[g(w(ae),{class:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"},{default:b(()=>[n[3]||(n[3]=k(" 管理员 ")),g(w(ut),{class:"ml-1 h-4 w-4"})]),_:1,__:[3]})]),g(Q,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:b(()=>[g(w(oe),{class:"absolute left-0 z-10 mt-2 w-56 origin-top-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:b(()=>[I("div",mt,[g(w(T),null,{default:b(({active:o})=>[g(r,{to:"/admin",class:M([o?"bg-gray-100 text-gray-900":"text-gray-700","block px-4 py-2 text-sm"])},{default:b(()=>n[4]||(n[4]=[k(" 管理员仪表板 ")])),_:2,__:[4]},1032,["class"])]),_:1}),g(w(T),null,{default:b(({active:o})=>[g(r,{to:"/admin/users",class:M([o?"bg-gray-100 text-gray-900":"text-gray-700","block px-4 py-2 text-sm"])},{default:b(()=>n[5]||(n[5]=[k(" 用户管理 ")])),_:2,__:[5]},1032,["class"])]),_:1}),g(w(T),null,{default:b(({active:o})=>[g(r,{to:"/admin/products",class:M([o?"bg-gray-100 text-gray-900":"text-gray-700","block px-4 py-2 text-sm"])},{default:b(()=>n[6]||(n[6]=[k(" 产品管理 ")])),_:2,__:[6]},1032,["class"])]),_:1}),g(w(T),null,{default:b(({active:o})=>[g(r,{to:"/admin/licenses",class:M([o?"bg-gray-100 text-gray-900":"text-gray-700","block px-4 py-2 text-sm"])},{default:b(()=>n[7]||(n[7]=[k(" 授权管理 ")])),_:2,__:[7]},1032,["class"])]),_:1}),g(w(T),null,{default:b(({active:o})=>[g(r,{to:"/admin/logs",class:M([o?"bg-gray-100 text-gray-900":"text-gray-700","block px-4 py-2 text-sm"])},{default:b(()=>n[8]||(n[8]=[k(" 操作日志 ")])),_:2,__:[8]},1032,["class"])]),_:1}),g(w(T),null,{default:b(({active:o})=>[g(r,{to:"/admin/stats",class:M([o?"bg-gray-100 text-gray-900":"text-gray-700","block px-4 py-2 text-sm"])},{default:b(()=>n[9]||(n[9]=[k(" 统计分析 ")])),_:2,__:[9]},1032,["class"])]),_:1})])]),_:1})]),_:1})]),_:1})])):Se("",!0)])]),I("div",gt,[g(w(le),{as:"div",class:"ml-3 relative"},{default:b(()=>[I("div",null,[g(w(ae),{class:"bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:b(()=>{var o,d;return[n[11]||(n[11]=I("span",{class:"sr-only"},"Open user menu",-1)),I("div",bt,[I("span",yt,ke((d=(o=w(t).user)==null?void 0:o.email)==null?void 0:d.charAt(0).toUpperCase()),1)])]}),_:1,__:[11]})]),g(Q,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:b(()=>[g(w(oe),{class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:b(()=>[g(w(T),null,{default:b(({active:o})=>[g(r,{to:"/profile",class:M([o?"bg-gray-100":"","block px-4 py-2 text-sm text-gray-700"])},{default:b(()=>n[12]||(n[12]=[k(" 个人资料 ")])),_:2,__:[12]},1032,["class"])]),_:1}),g(w(T),null,{default:b(({active:o})=>[I("button",{onClick:a,class:M([o?"bg-gray-100":"","block w-full text-left px-4 py-2 text-sm text-gray-700"])}," 退出登录 ",2)]),_:1})]),_:1})]),_:1})]),_:1})])])])]),I("main",ht,[Ee(s.$slots,"default")])])}}});export{It as _};
