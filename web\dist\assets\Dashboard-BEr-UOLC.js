import{_ as j}from"./Layout.vue_vue_type_script_setup_true_lang-DaBFh0gi.js";import{c as n,o as i,b as t,d as B,u as C,r as M,k as $,l as S,m as V,n as A,p as c,q as H,t as a,h as r,g as o,e as N,s as Z}from"./index-DgD6jlCj.js";import{r as D}from"./CheckCircleIcon-CvQFFXss.js";import{r as z}from"./CubeIcon-9x93tKpn.js";import{r as E}from"./KeyIcon-CW4MztXx.js";function k(u,l){return i(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}function F(u,l){return i(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}function P(u,l){return i(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}const G={class:"px-4 py-6 sm:px-0"},I={class:"mb-6"},J={class:"mt-1 text-sm text-gray-600"},K={class:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8"},L={class:"card"},O={class:"card-body"},Q={class:"flex items-center"},R={class:"flex-shrink-0"},T={class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},U={class:"ml-5 w-0 flex-1"},W={class:"text-lg font-medium text-gray-900"},X={class:"card"},Y={class:"card-body"},tt={class:"flex items-center"},st={class:"flex-shrink-0"},et={class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},ot={class:"ml-5 w-0 flex-1"},at={class:"text-lg font-medium text-gray-900"},rt={class:"card"},it={class:"card-body"},dt={class:"flex items-center"},nt={class:"flex-shrink-0"},lt={class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},ct={class:"ml-5 w-0 flex-1"},ut={class:"text-lg font-medium text-gray-900"},mt={class:"card"},_t={class:"card-body"},ht={class:"flex items-center"},xt={class:"flex-shrink-0"},ft={class:"w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center"},gt={class:"ml-5 w-0 flex-1"},vt={class:"text-lg font-medium text-gray-900"},pt={class:"card mb-8"},wt={class:"card-body"},yt={class:"mb-2 flex justify-between text-sm"},bt={class:"text-gray-600"},kt={class:"text-gray-600"},qt={class:"w-full bg-gray-200 rounded-full h-2"},jt={key:0,class:"mt-2 text-sm text-red-600"},Bt={key:1,class:"mt-2 text-sm text-orange-600"},Ct={class:"card"},Mt={class:"card-body"},$t={class:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3"},St={class:"flex-shrink-0"},Vt={class:"flex-shrink-0"},At={class:"flex-shrink-0"},Et=B({__name:"Dashboard",setup(u){const l=C(),e=M(null),m=$(()=>!e.value||e.value.total_quota===0?0:Math.round(e.value.used_quota/e.value.total_quota*100)),q=async()=>{try{const d=await A.get("/stats/dashboard");d.data.success&&(e.value=d.data.data)}catch(d){console.error("Failed to fetch dashboard stats:",d)}};return S(()=>{q()}),(d,s)=>{const _=H("router-link");return i(),V(j,null,{default:c(()=>{var h,x,f,g,v,p,w,y,b;return[t("div",G,[t("div",I,[s[0]||(s[0]=t("h1",{class:"text-2xl font-bold text-gray-900"},"仪表板",-1)),t("p",J," 欢迎回来，"+a((h=r(l).user)==null?void 0:h.email),1)]),t("div",K,[t("div",L,[t("div",O,[t("div",Q,[t("div",R,[t("div",T,[o(r(D),{class:"w-5 h-5 text-white"})])]),t("div",U,[t("dl",null,[s[1]||(s[1]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"剩余配额",-1)),t("dd",W,a(((x=e.value)==null?void 0:x.remaining_quota)||0),1)])])])])]),t("div",X,[t("div",Y,[t("div",tt,[t("div",st,[t("div",et,[o(r(k),{class:"w-5 h-5 text-white"})])]),t("div",ot,[t("dl",null,[s[2]||(s[2]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"已用配额",-1)),t("dd",at,a(((f=e.value)==null?void 0:f.used_quota)||0),1)])])])])]),t("div",rt,[t("div",it,[t("div",dt,[t("div",nt,[t("div",lt,[o(r(z),{class:"w-5 h-5 text-white"})])]),t("div",ct,[t("dl",null,[s[3]||(s[3]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"总配额",-1)),t("dd",ut,a(((g=e.value)==null?void 0:g.total_quota)||0),1)])])])])]),t("div",mt,[t("div",_t,[t("div",ht,[t("div",xt,[t("div",ft,[o(r(E),{class:"w-5 h-5 text-white"})])]),t("div",gt,[t("dl",null,[s[4]||(s[4]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"活跃授权",-1)),t("dd",vt,a(((v=e.value)==null?void 0:v.active_licenses)||0),1)])])])])])]),t("div",pt,[s[5]||(s[5]=t("div",{class:"card-header"},[t("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"配额使用情况")],-1)),t("div",wt,[t("div",yt,[t("span",bt,"已使用 "+a(((p=e.value)==null?void 0:p.used_quota)||0)+" / "+a(((w=e.value)==null?void 0:w.total_quota)||0),1),t("span",kt,a(m.value)+"%",1)]),t("div",qt,[t("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:Z({width:m.value+"%"})},null,4)]),((y=e.value)==null?void 0:y.remaining_quota)===0?(i(),n("div",jt," ⚠️ 您的配额已用完，请联系管理员增加配额 ")):m.value>80?(i(),n("div",Bt," ⚠️ 您的配额即将用完，剩余 "+a((b=e.value)==null?void 0:b.remaining_quota)+" 个 ",1)):N("",!0)])]),t("div",Ct,[s[9]||(s[9]=t("div",{class:"card-header"},[t("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"快速操作")],-1)),t("div",Mt,[t("div",$t,[o(_,{to:"/licenses/request",class:"relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"},{default:c(()=>[t("div",St,[o(r(F),{class:"h-6 w-6 text-primary-600"})]),s[6]||(s[6]=t("div",{class:"flex-1 min-w-0"},[t("span",{class:"absolute inset-0","aria-hidden":"true"}),t("p",{class:"text-sm font-medium text-gray-900"},"申请新授权"),t("p",{class:"text-sm text-gray-500"},"为您的产品申请授权码")],-1))]),_:1,__:[6]}),o(_,{to:"/licenses",class:"relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"},{default:c(()=>[t("div",Vt,[o(r(k),{class:"h-6 w-6 text-primary-600"})]),s[7]||(s[7]=t("div",{class:"flex-1 min-w-0"},[t("span",{class:"absolute inset-0","aria-hidden":"true"}),t("p",{class:"text-sm font-medium text-gray-900"},"查看授权记录"),t("p",{class:"text-sm text-gray-500"},"管理您的授权历史")],-1))]),_:1,__:[7]}),o(_,{to:"/profile",class:"relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"},{default:c(()=>[t("div",At,[o(r(P),{class:"h-6 w-6 text-primary-600"})]),s[8]||(s[8]=t("div",{class:"flex-1 min-w-0"},[t("span",{class:"absolute inset-0","aria-hidden":"true"}),t("p",{class:"text-sm font-medium text-gray-900"},"个人资料"),t("p",{class:"text-sm text-gray-500"},"更新您的账户信息")],-1))]),_:1,__:[8]})])])])])]}),_:1})}}});export{Et as default};
