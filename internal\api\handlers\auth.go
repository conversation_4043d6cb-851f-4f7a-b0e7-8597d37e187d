package handlers

import (
	"net/http"
	"licmanager/internal/models"
	"licmanager/internal/utils"

	"github.com/gin-gonic/gin"
)

// LoginRequest 登录请求结构
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Token string      `json:"token"`
	User  models.User `json:"user"`
}

// Login 用户登录
func (h *Handlers) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	// 查找用户
	var user models.User
	if err := h.DB.Where("email = ? AND is_active = ?", req.Email, true).First(&user).Error; err != nil {
		c.J<PERSON>(http.StatusUnauthorized, ErrorResponse("Invalid email or password"))
		return
	}

	// 验证密码
	if !utils.CheckPasswordHash(req.Password, user.Password) {
		c.JSON(http.StatusUnauthorized, ErrorResponse("Invalid email or password"))
		return
	}

	// 生成JWT令牌
	token, err := utils.GenerateJWT(user.ID, user.Email, user.IsAdmin, h.Config.JWTSecret)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to generate token"))
		return
	}

	// 清除密码字段
	user.Password = ""

	response := LoginResponse{
		Token: token,
		User:  user,
	}

	c.JSON(http.StatusOK, SuccessResponse(response))
}

// GetProfile 获取用户资料
func (h *Handlers) GetProfile(c *gin.Context) {
	userID := c.GetUint("user_id")

	var user models.User
	if err := h.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("User not found"))
		return
	}

	// 清除密码字段
	user.Password = ""

	c.JSON(http.StatusOK, SuccessResponse(user))
}

// UpdateProfileRequest 更新资料请求结构
type UpdateProfileRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// UpdateProfile 更新用户资料
func (h *Handlers) UpdateProfile(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	// 检查邮箱是否已被其他用户使用
	var existingUser models.User
	if err := h.DB.Where("email = ? AND id != ?", req.Email, userID).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusConflict, ErrorResponse("Email already in use"))
		return
	}

	// 更新用户信息
	if err := h.DB.Model(&models.User{}).Where("id = ?", userID).Update("email", req.Email).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to update profile"))
		return
	}

	c.JSON(http.StatusOK, SuccessMessageResponse("Profile updated successfully", nil))
}

// ChangePasswordRequest 修改密码请求结构
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=6"`
}

// ChangePassword 修改密码
func (h *Handlers) ChangePassword(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	// 获取用户当前密码
	var user models.User
	if err := h.DB.First(&user, userID).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("User not found"))
		return
	}

	// 验证当前密码
	if !utils.CheckPasswordHash(req.CurrentPassword, user.Password) {
		c.JSON(http.StatusBadRequest, ErrorResponse("Current password is incorrect"))
		return
	}

	// 验证新密码强度
	if err := utils.ValidatePasswordStrength(req.NewPassword); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err.Error()))
		return
	}

	// 检查新密码是否与当前密码相同
	if utils.CheckPasswordHash(req.NewPassword, user.Password) {
		c.JSON(http.StatusBadRequest, ErrorResponse("New password cannot be the same as current password"))
		return
	}

	// 哈希新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to hash password"))
		return
	}

	// 更新密码
	if err := h.DB.Model(&user).Update("password", hashedPassword).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to update password"))
		return
	}

	c.JSON(http.StatusOK, SuccessMessageResponse("Password changed successfully", nil))
}
