<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">统计分析</h1>
        <p class="mt-1 text-sm text-gray-600">查看详细的统计数据和趋势分析</p>
      </div>

      <!-- 时间范围选择 -->
      <div class="mb-6 card">
        <div class="card-body">
          <div class="flex items-center space-x-4">
            <label class="form-label">统计时间范围：</label>
            <select
              v-model="selectedDays"
              @change="fetchStats"
              class="form-input w-auto"
            >
              <option value="7">最近7天</option>
              <option value="30">最近30天</option>
              <option value="90">最近90天</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 用户注册趋势 -->
      <div class="mb-8 card">
        <div class="card-header">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            用户注册趋势
          </h3>
        </div>
        <div class="card-body">
          <div v-if="userStatsLoading" class="text-center py-8">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"
            ></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>
          <div v-else-if="userStats.length === 0" class="text-center py-8">
            <p class="text-gray-500">暂无数据</p>
          </div>
          <div v-else class="space-y-4">
            <!-- 简单的图表展示 -->
            <div class="grid grid-cols-1 gap-4">
              <div
                v-for="stat in userStats"
                :key="stat.date"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div class="text-sm font-medium text-gray-900">
                  {{ formatDate(stat.date) }}
                </div>
                <div class="flex items-center">
                  <div class="text-sm text-gray-600 mr-2">
                    {{ stat.count }} 个新用户
                  </div>
                  <div
                    class="h-2 bg-blue-500 rounded"
                    :style="{
                      width: getBarWidth(stat.count, maxUserCount) + 'px',
                    }"
                  ></div>
                </div>
              </div>
            </div>
            <div class="text-xs text-gray-500 text-center">
              总计：{{
                userStats.reduce((sum, stat) => sum + stat.count, 0)
              }}
              个新用户
            </div>
          </div>
        </div>
      </div>

      <!-- 授权申请趋势 -->
      <div class="mb-8 card">
        <div class="card-header">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            授权申请趋势
          </h3>
        </div>
        <div class="card-body">
          <div v-if="licenseStatsLoading" class="text-center py-8">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"
            ></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>
          <div v-else-if="licenseStats.length === 0" class="text-center py-8">
            <p class="text-gray-500">暂无数据</p>
          </div>
          <div v-else class="space-y-4">
            <!-- 按状态分组显示 -->
            <div class="grid grid-cols-1 gap-4">
              <div
                v-for="stat in licenseStats"
                :key="`${stat.date}-${stat.status}`"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <div class="text-sm font-medium text-gray-900">
                    {{ formatDate(stat.date) }}
                  </div>
                  <span
                    :class="[
                      'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                      stat.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800',
                    ]"
                  >
                    {{ stat.status === "active" ? "有效" : "已撤销" }}
                  </span>
                </div>
                <div class="flex items-center">
                  <div class="text-sm text-gray-600 mr-2">
                    {{ stat.count }} 个授权
                  </div>
                  <div
                    :class="[
                      'h-2 rounded',
                      stat.status === 'active' ? 'bg-green-500' : 'bg-red-500',
                    ]"
                    :style="{
                      width: getBarWidth(stat.count, maxLicenseCount) + 'px',
                    }"
                  ></div>
                </div>
              </div>
            </div>
            <div class="text-xs text-gray-500 text-center">
              总计：{{
                licenseStats.reduce((sum, stat) => sum + stat.count, 0)
              }}
              个授权申请
            </div>
          </div>
        </div>
      </div>

      <!-- 数据汇总 -->
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"
                >
                  <UsersIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    新增用户
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ userStats.reduce((sum, stat) => sum + stat.count, 0) }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"
                >
                  <KeyIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    新增授权
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{
                      licenseStats.reduce((sum, stat) => sum + stat.count, 0)
                    }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"
                >
                  <CheckCircleIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    有效授权
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{
                      licenseStats
                        .filter((s) => s.status === "active")
                        .reduce((sum, stat) => sum + stat.count, 0)
                    }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center"
                >
                  <XCircleIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    已撤销
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{
                      licenseStats
                        .filter((s) => s.status === "revoked")
                        .reduce((sum, stat) => sum + stat.count, 0)
                    }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import Layout from "@/components/Layout.vue";
import api from "@/utils/api";
import type { ApiResponse } from "@/types";
import {
  UsersIcon,
  KeyIcon,
  CheckCircleIcon,
  XCircleIcon,
} from "@heroicons/vue/24/outline";

interface UserStatsItem {
  date: string;
  count: number;
}

interface LicenseStatsItem {
  date: string;
  count: number;
  status: string;
}

const selectedDays = ref(30);
const userStatsLoading = ref(false);
const licenseStatsLoading = ref(false);
const userStats = ref<UserStatsItem[]>([]);
const licenseStats = ref<LicenseStatsItem[]>([]);

const maxUserCount = computed(() => {
  return Math.max(...userStats.value.map((s) => s.count), 1);
});

const maxLicenseCount = computed(() => {
  return Math.max(...licenseStats.value.map((s) => s.count), 1);
});

const fetchUserStats = async () => {
  userStatsLoading.value = true;
  try {
    const response = await api.get<ApiResponse<UserStatsItem[]>>(
      "/admin/stats/users",
      {
        params: { days: selectedDays.value },
      }
    );
    if (response.data.success) {
      userStats.value = response.data.data || [];
    }
  } catch (error) {
    console.error("Failed to fetch user stats:", error);
  } finally {
    userStatsLoading.value = false;
  }
};

const fetchLicenseStats = async () => {
  licenseStatsLoading.value = true;
  try {
    const response = await api.get<ApiResponse<LicenseStatsItem[]>>(
      "/admin/stats/licenses",
      {
        params: { days: selectedDays.value },
      }
    );
    if (response.data.success) {
      licenseStats.value = response.data.data || [];
    }
  } catch (error) {
    console.error("Failed to fetch license stats:", error);
  } finally {
    licenseStatsLoading.value = false;
  }
};

const fetchStats = async () => {
  await Promise.all([fetchUserStats(), fetchLicenseStats()]);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("zh-CN");
};

const getBarWidth = (value: number, max: number) => {
  return Math.max((value / max) * 200, 10); // 最小宽度10px，最大200px
};

onMounted(() => {
  fetchStats();
});
</script>
