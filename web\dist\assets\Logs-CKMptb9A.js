import{_ as a}from"./Layout.vue_vue_type_script_setup_true_lang-DaBFh0gi.js";import{d as e,m as o,p as r,b as s,o as c}from"./index-DgD6jlCj.js";const n=e({__name:"Logs",setup(l){return(p,t)=>(c(),o(a,null,{default:r(()=>t[0]||(t[0]=[s("div",{class:"px-4 py-6 sm:px-0"},[s("div",{class:"mb-6"},[s("h1",{class:"text-2xl font-bold text-gray-900"},"操作日志"),s("p",{class:"mt-1 text-sm text-gray-600"}," 查看管理员的操作记录和审计日志 ")]),s("div",{class:"card"},[s("div",{class:"card-body"},[s("p",{class:"text-gray-500"},"操作日志功能正在开发中...")])])],-1)])),_:1,__:[0]}))}});export{n as default};
