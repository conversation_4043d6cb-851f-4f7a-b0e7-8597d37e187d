package main

import (
	"log"
	"licmanager/internal/api"
	"licmanager/internal/config"
	"licmanager/internal/database"
)

func main() {
	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Initialize(cfg.DatabasePath)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 创建默认超级管理员
	if err := database.CreateDefaultAdmin(db); err != nil {
		log.Printf("Warning: Failed to create default admin: %v", err)
	}

	// 启动API服务器
	router := api.SetupRouter(db, cfg)
	
	log.Printf("Server starting on port %s", cfg.Port)
	if err := router.Run(":" + cfg.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
