<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
              <CogIcon class="h-6 w-6 text-green-600" />
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                调整用户配额
              </h3>
              <p class="mt-1 text-sm text-gray-600">
                为用户 {{ user.email }} 调整授权配额
              </p>
              <div class="mt-4">
                <form @submit.prevent="handleSubmit" class="space-y-4">
                  <!-- 当前配额信息 -->
                  <div class="bg-gray-50 p-4 rounded-md">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">当前配额状态</h4>
                    <div class="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div class="text-lg font-semibold text-blue-600">{{ user.max_license_quota }}</div>
                        <div class="text-xs text-gray-500">总配额</div>
                      </div>
                      <div>
                        <div class="text-lg font-semibold text-orange-600">{{ user.used_license_quota }}</div>
                        <div class="text-xs text-gray-500">已使用</div>
                      </div>
                      <div>
                        <div class="text-lg font-semibold text-green-600">{{ user.max_license_quota - user.used_license_quota }}</div>
                        <div class="text-xs text-gray-500">剩余</div>
                      </div>
                    </div>
                    <div class="mt-3">
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          class="bg-primary-600 h-2 rounded-full"
                          :style="{ width: getUsagePercentage() + '%' }"
                        ></div>
                      </div>
                      <div class="text-xs text-gray-500 mt-1 text-center">
                        使用率：{{ getUsagePercentage() }}%
                      </div>
                    </div>
                  </div>

                  <!-- 新配额设置 -->
                  <div>
                    <label class="form-label">新的最大配额 *</label>
                    <input
                      v-model.number="form.max_license_quota"
                      type="number"
                      required
                      :min="user.used_license_quota"
                      class="form-input"
                      placeholder="请输入新的最大配额"
                    />
                    <p class="mt-1 text-sm text-gray-500">
                      不能小于已使用配额 ({{ user.used_license_quota }})
                    </p>
                  </div>

                  <!-- 调整原因 -->
                  <div>
                    <label class="form-label">调整原因 *</label>
                    <textarea
                      v-model="form.reason"
                      required
                      rows="3"
                      class="form-input"
                      placeholder="请说明配额调整的原因..."
                    ></textarea>
                  </div>

                  <!-- 配额变化预览 -->
                  <div v-if="form.max_license_quota !== user.max_license_quota" class="bg-blue-50 p-4 rounded-md">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">配额变化预览</h4>
                    <div class="text-sm text-blue-800">
                      <div class="flex justify-between">
                        <span>当前配额：</span>
                        <span>{{ user.max_license_quota }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span>新配额：</span>
                        <span>{{ form.max_license_quota }}</span>
                      </div>
                      <div class="flex justify-between font-medium">
                        <span>变化：</span>
                        <span :class="quotaChange >= 0 ? 'text-green-600' : 'text-red-600'">
                          {{ quotaChange >= 0 ? '+' : '' }}{{ quotaChange }}
                        </span>
                      </div>
                      <div class="flex justify-between">
                        <span>新剩余：</span>
                        <span>{{ form.max_license_quota - user.used_license_quota }}</span>
                      </div>
                    </div>
                  </div>

                  <div v-if="error" class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">调整失败</h3>
                        <div class="mt-2 text-sm text-red-700">{{ error }}</div>
                      </div>
                    </div>
                  </div>

                  <div v-if="success" class="rounded-md bg-green-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <CheckCircleIcon class="h-5 w-5 text-green-400" />
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">配额调整成功</h3>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="handleSubmit"
            :disabled="loading || !canSubmit"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
          >
            <span v-if="loading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              调整中...
            </span>
            <span v-else>确认调整</span>
          </button>
          <button
            @click="$emit('close')"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import api from '@/utils/api'
import type { User, UpdateUserQuotaRequest, ApiResponse } from '@/types'
import { 
  CogIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon 
} from '@heroicons/vue/24/outline'

interface Props {
  user: User
}

const props = defineProps<Props>()

defineEmits<{
  close: []
  success: []
}>()

const loading = ref(false)
const error = ref('')
const success = ref(false)

const form = reactive<UpdateUserQuotaRequest>({
  max_license_quota: 0,
  reason: ''
})

const quotaChange = computed(() => form.max_license_quota - props.user.max_license_quota)

const canSubmit = computed(() => {
  return form.max_license_quota >= props.user.used_license_quota && 
         form.reason.trim().length > 0 &&
         form.max_license_quota !== props.user.max_license_quota
})

const getUsagePercentage = () => {
  if (props.user.max_license_quota === 0) return 0
  return Math.round((props.user.used_license_quota / props.user.max_license_quota) * 100)
}

const handleSubmit = async () => {
  if (loading.value || !canSubmit.value) return
  
  loading.value = true
  error.value = ''
  success.value = false
  
  try {
    const response = await api.put<ApiResponse>(`/admin/users/${props.user.id}/quota`, form)
    
    if (response.data.success) {
      success.value = true
      
      // 1秒后触发成功事件
      setTimeout(() => {
        // $emit('success')
      }, 1000)
    } else {
      error.value = response.data.error || '调整失败'
    }
  } catch (err: any) {
    error.value = err.response?.data?.error || '调整失败，请检查输入信息'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  form.max_license_quota = props.user.max_license_quota
})
</script>
