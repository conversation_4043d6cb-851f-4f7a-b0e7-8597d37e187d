<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-900">产品管理</h1>
          <p class="mt-2 text-sm text-gray-700">管理系统中的产品分类和信息</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button @click="showCreateModal = true" class="btn-primary">
            添加产品
          </button>
        </div>
      </div>

      <!-- 产品列表 -->
      <div class="mt-8 card">
        <div class="card-body">
          <div v-if="loading" class="text-center py-8">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"
            ></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>

          <div v-else-if="products.length === 0" class="text-center py-8">
            <CubeIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无产品</h3>
            <p class="mt-1 text-sm text-gray-500">开始添加第一个产品</p>
            <div class="mt-6">
              <button @click="showCreateModal = true" class="btn-primary">
                添加产品
              </button>
            </div>
          </div>

          <div
            v-else
            class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3"
          >
            <div
              v-for="product in products"
              :key="product.id"
              class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <h3 class="text-lg font-medium text-gray-900 truncate">
                      {{ product.name }}
                    </h3>
                    <span
                      :class="[
                        'ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        product.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800',
                      ]"
                    >
                      {{ product.is_active ? "启用" : "禁用" }}
                    </span>
                  </div>
                  <p class="mt-1 text-sm text-gray-600">
                    {{ product.category }}
                  </p>
                  <p class="mt-2 text-sm text-gray-500">
                    {{ product.description || "暂无描述" }}
                  </p>
                  <p class="mt-2 text-xs text-gray-400">
                    创建时间：{{ formatDate(product.created_at) }}
                  </p>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="relative">
                    <Menu as="div" class="relative inline-block text-left">
                      <div>
                        <MenuButton
                          class="flex items-center text-gray-400 hover:text-gray-600"
                        >
                          <EllipsisVerticalIcon class="h-5 w-5" />
                        </MenuButton>
                      </div>
                      <transition
                        enter-active-class="transition ease-out duration-100"
                        enter-from-class="transform opacity-0 scale-95"
                        enter-to-class="transform opacity-100 scale-100"
                        leave-active-class="transition ease-in duration-75"
                        leave-from-class="transform opacity-100 scale-100"
                        leave-to-class="transform opacity-0 scale-95"
                      >
                        <MenuItems
                          class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                        >
                          <div class="py-1">
                            <MenuItem v-slot="{ active }">
                              <button
                                @click="editProduct(product)"
                                :class="[
                                  active
                                    ? 'bg-gray-100 text-gray-900'
                                    : 'text-gray-700',
                                  'block w-full text-left px-4 py-2 text-sm',
                                ]"
                              >
                                编辑
                              </button>
                            </MenuItem>
                            <MenuItem v-slot="{ active }">
                              <button
                                @click="toggleProductStatus(product)"
                                :class="[
                                  active
                                    ? 'bg-gray-100 text-gray-900'
                                    : 'text-gray-700',
                                  'block w-full text-left px-4 py-2 text-sm',
                                ]"
                              >
                                {{ product.is_active ? "禁用" : "启用" }}
                              </button>
                            </MenuItem>
                            <MenuItem v-slot="{ active }">
                              <button
                                @click="deleteProduct(product)"
                                :class="[
                                  active
                                    ? 'bg-gray-100 text-gray-900'
                                    : 'text-gray-700',
                                  'block w-full text-left px-4 py-2 text-sm',
                                ]"
                              >
                                删除
                              </button>
                            </MenuItem>
                          </div>
                        </MenuItems>
                      </transition>
                    </Menu>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建产品模态框 -->
    <CreateProductModal
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @success="handleCreateSuccess"
    />

    <!-- 编辑产品模态框 -->
    <EditProductModal
      v-if="showEditModal && selectedProduct"
      :product="selectedProduct"
      @close="showEditModal = false"
      @success="handleEditSuccess"
    />
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import Layout from "@/components/Layout.vue";
import CreateProductModal from "@/components/admin/CreateProductModal.vue";
import EditProductModal from "@/components/admin/EditProductModal.vue";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/vue";
import api from "@/utils/api";
import type { Product, ApiResponse } from "@/types";
import { CubeIcon, EllipsisVerticalIcon } from "@heroicons/vue/24/outline";

const loading = ref(false);
const products = ref<Product[]>([]);

const showCreateModal = ref(false);
const showEditModal = ref(false);
const selectedProduct = ref<Product | null>(null);

const fetchProducts = async () => {
  loading.value = true;
  try {
    const response = await api.get<ApiResponse<Product[]>>("/products");
    if (response.data.success) {
      products.value = response.data.data || [];
    }
  } catch (error) {
    console.error("Failed to fetch products:", error);
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("zh-CN");
};

const editProduct = (product: Product) => {
  selectedProduct.value = product;
  showEditModal.value = true;
};

const toggleProductStatus = async (product: Product) => {
  try {
    const response = await api.put<ApiResponse>(
      `/admin/products/${product.id}`,
      {
        ...product,
        is_active: !product.is_active,
      }
    );
    if (response.data.success) {
      await fetchProducts();
    } else {
      alert(response.data.error || "操作失败");
    }
  } catch (error: any) {
    alert(error.response?.data?.error || "操作失败");
  }
};

const deleteProduct = async (product: Product) => {
  if (!confirm(`确定要删除产品 ${product.name} 吗？此操作不可恢复。`)) {
    return;
  }

  try {
    const response = await api.delete<ApiResponse>(
      `/admin/products/${product.id}`
    );
    if (response.data.success) {
      await fetchProducts();
    } else {
      alert(response.data.error || "删除失败");
    }
  } catch (error: any) {
    alert(error.response?.data?.error || "删除失败");
  }
};

const handleCreateSuccess = () => {
  showCreateModal.value = false;
  fetchProducts();
};

const handleEditSuccess = () => {
  showEditModal.value = false;
  selectedProduct.value = null;
  fetchProducts();
};

onMounted(() => {
  fetchProducts();
});
</script>
