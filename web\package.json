{"name": "licmanager-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.11", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "chart.js": "^4.4.1", "vue-chartjs": "^5.3.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18"}, "devDependencies": {"@types/node": "^20.10.5", "@vitejs/plugin-vue": "^4.5.2", "typescript": "^5.2.2", "vue-tsc": "^1.8.25", "vite": "^5.0.8", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}