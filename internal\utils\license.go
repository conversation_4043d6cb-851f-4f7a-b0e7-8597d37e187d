package utils

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"
)

// GenerateLicenseCode 根据机器码生成授权码
func GenerateLicenseCode(machineCode string, productID uint, userID uint) string {
	// 组合输入数据
	input := fmt.Sprintf("%s-%d-%d-%d", machineCode, productID, userID, time.Now().Unix())
	
	// 使用SHA256生成哈希
	hash := sha256.Sum256([]byte(input))
	hashStr := hex.EncodeToString(hash[:])
	
	// 格式化为更易读的授权码格式 (XXXX-XXXX-XXXX-XXXX)
	licenseCode := strings.ToUpper(hashStr[:16])
	return fmt.Sprintf("%s-%s-%s-%s", 
		licenseCode[0:4], 
		licenseCode[4:8], 
		licenseCode[8:12], 
		licenseCode[12:16])
}

// ValidateMachineCode 验证机器码格式
func ValidateMachineCode(machineCode string) bool {
	// 机器码应该是32位十六进制字符串
	if len(machineCode) != 32 {
		return false
	}
	
	// 检查是否为有效的十六进制
	_, err := hex.DecodeString(machineCode)
	return err == nil
}

// GenerateMachineCodeHash 生成机器码的哈希值用于快速查找
func GenerateMachineCodeHash(machineCode string) string {
	hash := md5.Sum([]byte(machineCode))
	return hex.EncodeToString(hash[:])
}

// FormatLicenseCode 格式化授权码显示
func FormatLicenseCode(code string) string {
	// 移除所有非字母数字字符
	clean := strings.ReplaceAll(code, "-", "")
	clean = strings.ToUpper(clean)
	
	if len(clean) >= 16 {
		return fmt.Sprintf("%s-%s-%s-%s", 
			clean[0:4], 
			clean[4:8], 
			clean[8:12], 
			clean[12:16])
	}
	
	return clean
}
