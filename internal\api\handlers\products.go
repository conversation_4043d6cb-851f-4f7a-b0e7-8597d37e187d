package handlers

import (
	"net/http"
	"strconv"
	"licmanager/internal/models"

	"github.com/gin-gonic/gin"
)

// GetProducts 获取产品列表
func (h *Handlers) GetProducts(c *gin.Context) {
	var products []models.Product
	if err := h.DB.Where("is_active = ?", true).Find(&products).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to fetch products"))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(products))
}

// GetProduct 获取单个产品
func (h *Handlers) GetProduct(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid product ID"))
		return
	}

	var product models.Product
	if err := h.DB.Where("id = ? AND is_active = ?", id, true).First(&product).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("Product not found"))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(product))
}

// CreateProductRequest 创建产品请求结构
type CreateProductRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Category    string `json:"category"`
}

// CreateProduct 创建产品 (管理员)
func (h *Handlers) CreateProduct(c *gin.Context) {
	var req CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	product := models.Product{
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		IsActive:    true,
	}

	if err := h.DB.Create(&product).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to create product"))
		return
	}

	// 记录管理员操作日志
	h.logAdminAction(c, "CREATE_PRODUCT", "Created product: "+product.Name, "", product.Name)

	c.JSON(http.StatusCreated, SuccessMessageResponse("Product created successfully", product))
}

// UpdateProductRequest 更新产品请求结构
type UpdateProductRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Category    string `json:"category"`
	IsActive    bool   `json:"is_active"`
}

// UpdateProduct 更新产品 (管理员)
func (h *Handlers) UpdateProduct(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid product ID"))
		return
	}

	var req UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	var product models.Product
	if err := h.DB.First(&product, id).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("Product not found"))
		return
	}

	oldName := product.Name
	product.Name = req.Name
	product.Description = req.Description
	product.Category = req.Category
	product.IsActive = req.IsActive

	if err := h.DB.Save(&product).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to update product"))
		return
	}

	// 记录管理员操作日志
	h.logAdminAction(c, "UPDATE_PRODUCT", "Updated product: "+product.Name, oldName, product.Name)

	c.JSON(http.StatusOK, SuccessMessageResponse("Product updated successfully", product))
}

// DeleteProduct 删除产品 (管理员)
func (h *Handlers) DeleteProduct(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid product ID"))
		return
	}

	var product models.Product
	if err := h.DB.First(&product, id).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("Product not found"))
		return
	}

	// 检查是否有关联的授权记录
	var licenseCount int64
	h.DB.Model(&models.LicenseRequest{}).Where("product_id = ?", id).Count(&licenseCount)
	if licenseCount > 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse("Cannot delete product with existing license records"))
		return
	}

	if err := h.DB.Delete(&product).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to delete product"))
		return
	}

	// 记录管理员操作日志
	h.logAdminAction(c, "DELETE_PRODUCT", "Deleted product: "+product.Name, product.Name, "")

	c.JSON(http.StatusOK, SuccessMessageResponse("Product deleted successfully", nil))
}
