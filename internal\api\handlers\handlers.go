package handlers

import (
	"licmanager/internal/config"
	"gorm.io/gorm"
)

// Handlers 处理器结构体
type Handlers struct {
	DB     *gorm.DB
	Config *config.Config
}

// NewHandlers 创建新的处理器实例
func NewHandlers(db *gorm.DB, cfg *config.Config) *Handlers {
	return &Handlers{
		DB:     db,
		Config: cfg,
	}
}

// Response 通用响应结构
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Meta    PaginationMeta `json:"meta"`
}

// PaginationMeta 分页元数据
type PaginationMeta struct {
	CurrentPage int   `json:"current_page"`
	PerPage     int   `json:"per_page"`
	Total       int64 `json:"total"`
	TotalPages  int   `json:"total_pages"`
}

// PaginationQuery 分页查询参数
type PaginationQuery struct {
	Page    int `form:"page,default=1" binding:"min=1"`
	PerPage int `form:"per_page,default=10" binding:"min=1,max=100"`
}

// SuccessResponse 成功响应
func SuccessResponse(data interface{}) Response {
	return Response{
		Success: true,
		Data:    data,
	}
}

// SuccessMessageResponse 成功消息响应
func SuccessMessageResponse(message string, data interface{}) Response {
	return Response{
		Success: true,
		Message: message,
		Data:    data,
	}
}

// ErrorResponse 错误响应
func ErrorResponse(error string) Response {
	return Response{
		Success: false,
		Error:   error,
	}
}

// PaginationSuccessResponse 分页成功响应
func PaginationSuccessResponse(data interface{}, meta PaginationMeta) PaginationResponse {
	return PaginationResponse{
		Success: true,
		Data:    data,
		Meta:    meta,
	}
}

// CalculatePaginationMeta 计算分页元数据
func CalculatePaginationMeta(page, perPage int, total int64) PaginationMeta {
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	return PaginationMeta{
		CurrentPage: page,
		PerPage:     perPage,
		Total:       total,
		TotalPages:  totalPages,
	}
}
