package handlers

import (
	"net/http"
	"strconv"
	"licmanager/internal/models"
	"licmanager/internal/utils"

	"github.com/gin-gonic/gin"
)

// GetAllUsers 获取所有用户 (管理员)
func (h *Handlers) GetAllUsers(c *gin.Context) {
	var pagination PaginationQuery
	if err := c.ShouldBindQuery(&pagination); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid pagination parameters"))
		return
	}

	var users []models.User
	var total int64

	// 计算总数
	h.DB.Model(&models.User{}).Count(&total)

	// 获取分页数据
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := h.DB.Select("id, email, is_admin, max_license_quota, used_license_quota, is_active, created_at, updated_at").
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&users).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to fetch users"))
		return
	}

	meta := CalculatePaginationMeta(pagination.Page, pagination.PerPage, total)
	c.JSON(http.StatusOK, PaginationSuccessResponse(users, meta))
}

// CreateUserRequest 创建用户请求结构
type CreateUserRequest struct {
	Email           string `json:"email" binding:"required,email"`
	MaxLicenseQuota int    `json:"max_license_quota" binding:"min=0"`
	IsAdmin         bool   `json:"is_admin"`
}

// CreateUser 创建用户 (管理员)
func (h *Handlers) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	// 检查邮箱是否已存在
	var existingUser models.User
	if err := h.DB.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusConflict, ErrorResponse("Email already exists"))
		return
	}

	// 生成随机密码
	password := utils.GenerateRandomPassword(12)
	hashedPassword, err := utils.HashPassword(password)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to hash password"))
		return
	}

	user := models.User{
		Email:           req.Email,
		Password:        hashedPassword,
		IsAdmin:         req.IsAdmin,
		MaxLicenseQuota: req.MaxLicenseQuota,
		IsActive:        true,
	}

	if err := h.DB.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to create user"))
		return
	}

	// 记录管理员操作日志
	h.logAdminAction(c, "CREATE_USER", "Created user: "+user.Email, "", user.Email)

	// 返回用户信息和临时密码
	response := map[string]interface{}{
		"user":     user,
		"password": password,
	}

	c.JSON(http.StatusCreated, SuccessMessageResponse("User created successfully", response))
}

// UpdateUserRequest 更新用户请求结构
type UpdateUserRequest struct {
	Email   string `json:"email" binding:"required,email"`
	IsAdmin bool   `json:"is_admin"`
	IsActive bool  `json:"is_active"`
}

// UpdateUser 更新用户 (管理员)
func (h *Handlers) UpdateUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid user ID"))
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	var user models.User
	if err := h.DB.First(&user, id).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("User not found"))
		return
	}

	// 检查邮箱是否已被其他用户使用
	var existingUser models.User
	if err := h.DB.Where("email = ? AND id != ?", req.Email, id).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusConflict, ErrorResponse("Email already in use"))
		return
	}

	oldEmail := user.Email
	user.Email = req.Email
	user.IsAdmin = req.IsAdmin
	user.IsActive = req.IsActive

	if err := h.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to update user"))
		return
	}

	// 记录管理员操作日志
	h.logAdminAction(c, "UPDATE_USER", "Updated user: "+user.Email, oldEmail, user.Email)

	// 清除密码字段
	user.Password = ""

	c.JSON(http.StatusOK, SuccessMessageResponse("User updated successfully", user))
}

// UpdateUserQuotaRequest 更新用户配额请求结构
type UpdateUserQuotaRequest struct {
	MaxLicenseQuota int    `json:"max_license_quota" binding:"min=0"`
	Reason          string `json:"reason" binding:"required"`
}

// UpdateUserQuota 更新用户配额 (管理员)
func (h *Handlers) UpdateUserQuota(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid user ID"))
		return
	}

	var req UpdateUserQuotaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid request data"))
		return
	}

	var user models.User
	if err := h.DB.First(&user, id).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("User not found"))
		return
	}

	// 检查新配额是否小于已使用配额
	if req.MaxLicenseQuota < user.UsedLicenseQuota {
		c.JSON(http.StatusBadRequest, ErrorResponse("New quota cannot be less than used quota"))
		return
	}

	oldQuota := user.MaxLicenseQuota
	user.MaxLicenseQuota = req.MaxLicenseQuota

	if err := h.DB.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to update user quota"))
		return
	}

	// 记录管理员操作日志
	description := "Updated user quota: " + user.Email + " - " + req.Reason
	h.logAdminActionWithTarget(c, uint(id), "UPDATE_QUOTA", description, 
		strconv.Itoa(oldQuota), strconv.Itoa(req.MaxLicenseQuota))

	// 清除密码字段
	user.Password = ""

	c.JSON(http.StatusOK, SuccessMessageResponse("User quota updated successfully", user))
}

// DeleteUser 删除用户 (管理员)
func (h *Handlers) DeleteUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse("Invalid user ID"))
		return
	}

	var user models.User
	if err := h.DB.First(&user, id).Error; err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse("User not found"))
		return
	}

	// 检查是否有关联的授权记录
	var licenseCount int64
	h.DB.Model(&models.LicenseRequest{}).Where("user_id = ?", id).Count(&licenseCount)
	if licenseCount > 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse("Cannot delete user with existing license records"))
		return
	}

	if err := h.DB.Delete(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse("Failed to delete user"))
		return
	}

	// 记录管理员操作日志
	h.logAdminAction(c, "DELETE_USER", "Deleted user: "+user.Email, user.Email, "")

	c.JSON(http.StatusOK, SuccessMessageResponse("User deleted successfully", nil))
}
