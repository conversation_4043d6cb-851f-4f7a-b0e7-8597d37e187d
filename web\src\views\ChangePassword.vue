<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="max-w-2xl mx-auto">
        <div class="mb-6">
          <h1 class="text-2xl font-bold text-gray-900">修改密码</h1>
          <p class="mt-1 text-sm text-gray-600">
            为了账户安全，建议定期更换密码
          </p>
        </div>

        <!-- 安全提示 -->
        <div class="mb-6 rounded-md bg-blue-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <InformationCircleIcon class="h-5 w-5 text-blue-400" />
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">
                密码安全提示
              </h3>
              <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>使用至少6个字符的复杂密码</li>
                  <li>包含大小写字母、数字和特殊字符</li>
                  <li>不要使用与个人信息相关的密码</li>
                  <li>定期更换密码以确保账户安全</li>
                  <li>不要在多个网站使用相同密码</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 密码修改表单 -->
        <ChangePasswordForm 
          :show-cancel="true"
          @cancel="handleCancel"
          @success="handleSuccess"
        />

        <!-- 最近密码修改记录 -->
        <div class="mt-8 card">
          <div class="card-header">
            <h3 class="text-lg leading-6 font-medium text-gray-900">安全记录</h3>
          </div>
          <div class="card-body">
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <div>
                  <p class="text-sm font-medium text-gray-900">账户创建时间</p>
                  <p class="text-sm text-gray-500">{{ formatDate(authStore.user?.created_at || '') }}</p>
                </div>
                <div class="text-sm text-gray-500">
                  <CheckCircleIcon class="h-5 w-5 text-green-500 inline" />
                  已验证
                </div>
              </div>
              
              <div class="flex justify-between items-center">
                <div>
                  <p class="text-sm font-medium text-gray-900">最后登录时间</p>
                  <p class="text-sm text-gray-500">{{ formatDate(new Date().toISOString()) }}</p>
                </div>
                <div class="text-sm text-gray-500">
                  <CheckCircleIcon class="h-5 w-5 text-green-500 inline" />
                  当前会话
                </div>
              </div>

              <div class="flex justify-between items-center">
                <div>
                  <p class="text-sm font-medium text-gray-900">账户类型</p>
                  <p class="text-sm text-gray-500">
                    {{ authStore.user?.is_admin ? '管理员账户' : '普通用户账户' }}
                  </p>
                </div>
                <div class="text-sm text-gray-500">
                  <span :class="authStore.user?.is_admin ? 'text-purple-600' : 'text-blue-600'">
                    {{ authStore.user?.is_admin ? '高级权限' : '标准权限' }}
                  </span>
                </div>
              </div>

              <div class="flex justify-between items-center">
                <div>
                  <p class="text-sm font-medium text-gray-900">账户状态</p>
                  <p class="text-sm text-gray-500">
                    {{ authStore.user?.is_active ? '正常' : '已禁用' }}
                  </p>
                </div>
                <div class="text-sm text-gray-500">
                  <span :class="authStore.user?.is_active ? 'text-green-600' : 'text-red-600'">
                    {{ authStore.user?.is_active ? '活跃' : '禁用' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 安全建议 -->
        <div class="mt-6 rounded-md bg-yellow-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <ExclamationTriangleIcon class="h-5 w-5 text-yellow-400" />
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">
                安全建议
              </h3>
              <div class="mt-2 text-sm text-yellow-700">
                <p>
                  如果您怀疑账户可能被盗用，请立即修改密码并联系管理员。
                  同时建议启用双因素认证以增强账户安全性。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import Layout from '@/components/Layout.vue'
import ChangePasswordForm from '@/components/ChangePasswordForm.vue'
import { useAuthStore } from '@/stores/auth'
import { 
  InformationCircleIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon 
} from '@heroicons/vue/24/outline'

const router = useRouter()
const authStore = useAuthStore()

const handleCancel = () => {
  router.back()
}

const handleSuccess = () => {
  // 密码修改成功后，可以选择跳转到其他页面
  setTimeout(() => {
    router.push('/profile')
  }, 2000)
}

const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>
