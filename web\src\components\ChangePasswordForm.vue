<template>
  <div class="card">
    <div class="card-header">
      <h3 class="text-lg leading-6 font-medium text-gray-900">修改密码</h3>
      <p class="mt-1 text-sm text-gray-600">
        为了账户安全，建议定期更换密码
      </p>
    </div>
    <div class="card-body">
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- 当前密码 -->
        <div>
          <label class="form-label">当前密码 *</label>
          <div class="relative">
            <input
              v-model="form.current_password"
              :type="showCurrentPassword ? 'text' : 'password'"
              required
              class="form-input pr-10"
              placeholder="请输入当前密码"
            />
            <button
              type="button"
              @click="showCurrentPassword = !showCurrentPassword"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <EyeIcon v-if="!showCurrentPassword" class="h-5 w-5 text-gray-400" />
              <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
            </button>
          </div>
        </div>

        <!-- 新密码 -->
        <div>
          <label class="form-label">新密码 *</label>
          <div class="relative">
            <input
              v-model="form.new_password"
              :type="showNewPassword ? 'text' : 'password'"
              required
              minlength="6"
              class="form-input pr-10"
              placeholder="请输入新密码"
              @input="validatePassword"
            />
            <button
              type="button"
              @click="showNewPassword = !showNewPassword"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <EyeIcon v-if="!showNewPassword" class="h-5 w-5 text-gray-400" />
              <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
            </button>
          </div>
          
          <!-- 密码强度指示器 -->
          <div v-if="form.new_password" class="mt-2">
            <div class="flex justify-between text-sm mb-1">
              <span class="text-gray-600">密码强度</span>
              <span :class="passwordStrengthColor">{{ passwordStrengthText }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                class="h-2 rounded-full transition-all duration-300"
                :class="passwordStrengthColor"
                :style="{ width: passwordStrengthPercentage + '%' }"
              ></div>
            </div>
          </div>

          <!-- 密码要求 -->
          <div class="mt-2 text-sm text-gray-500">
            <p>密码要求：</p>
            <ul class="list-disc list-inside mt-1 space-y-1">
              <li :class="form.new_password.length >= 6 ? 'text-green-600' : 'text-gray-500'">
                至少6个字符
              </li>
              <li :class="hasUpperCase ? 'text-green-600' : 'text-gray-500'">
                包含大写字母
              </li>
              <li :class="hasLowerCase ? 'text-green-600' : 'text-gray-500'">
                包含小写字母
              </li>
              <li :class="hasNumber ? 'text-green-600' : 'text-gray-500'">
                包含数字
              </li>
              <li :class="hasSpecialChar ? 'text-green-600' : 'text-gray-500'">
                包含特殊字符
              </li>
            </ul>
          </div>
        </div>

        <!-- 确认新密码 -->
        <div>
          <label class="form-label">确认新密码 *</label>
          <div class="relative">
            <input
              v-model="form.confirm_password"
              :type="showConfirmPassword ? 'text' : 'password'"
              required
              class="form-input pr-10"
              placeholder="请再次输入新密码"
              :class="form.confirm_password && form.new_password !== form.confirm_password ? 'border-red-300' : ''"
            />
            <button
              type="button"
              @click="showConfirmPassword = !showConfirmPassword"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <EyeIcon v-if="!showConfirmPassword" class="h-5 w-5 text-gray-400" />
              <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
            </button>
          </div>
          <p v-if="form.confirm_password && form.new_password !== form.confirm_password" 
             class="mt-1 text-sm text-red-600">
            两次输入的密码不一致
          </p>
        </div>

        <!-- 错误提示 -->
        <div v-if="error" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">修改失败</h3>
              <div class="mt-2 text-sm text-red-700">{{ error }}</div>
            </div>
          </div>
        </div>

        <!-- 成功提示 -->
        <div v-if="success" class="rounded-md bg-green-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <CheckCircleIcon class="h-5 w-5 text-green-400" />
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">密码修改成功</h3>
              <div class="mt-2 text-sm text-green-700">
                您的密码已成功更新，请妥善保管新密码
              </div>
            </div>
          </div>
        </div>

        <!-- 提交按钮 -->
        <div class="flex justify-end space-x-3">
          <button
            v-if="showCancel"
            type="button"
            @click="$emit('cancel')"
            class="btn-secondary"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="!canSubmit || loading"
            class="btn-primary disabled:opacity-50"
          >
            <span v-if="loading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              修改中...
            </span>
            <span v-else>修改密码</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { 
  ExclamationTriangleIcon, 
  CheckCircleIcon, 
  EyeIcon, 
  EyeSlashIcon 
} from '@heroicons/vue/24/outline'

interface Props {
  showCancel?: boolean
}

defineProps<Props>()

defineEmits<{
  cancel: []
  success: []
}>()

const authStore = useAuthStore()

const loading = ref(false)
const error = ref('')
const success = ref(false)

const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

const form = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 密码强度验证
const hasUpperCase = computed(() => /[A-Z]/.test(form.new_password))
const hasLowerCase = computed(() => /[a-z]/.test(form.new_password))
const hasNumber = computed(() => /\d/.test(form.new_password))
const hasSpecialChar = computed(() => /[!@#$%^&*(),.?":{}|<>]/.test(form.new_password))

const passwordStrength = computed(() => {
  let strength = 0
  if (form.new_password.length >= 6) strength++
  if (hasUpperCase.value) strength++
  if (hasLowerCase.value) strength++
  if (hasNumber.value) strength++
  if (hasSpecialChar.value) strength++
  return strength
})

const passwordStrengthPercentage = computed(() => (passwordStrength.value / 5) * 100)

const passwordStrengthText = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1: return '很弱'
    case 2: return '弱'
    case 3: return '中等'
    case 4: return '强'
    case 5: return '很强'
    default: return '很弱'
  }
})

const passwordStrengthColor = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1: return 'bg-red-500 text-red-600'
    case 2: return 'bg-orange-500 text-orange-600'
    case 3: return 'bg-yellow-500 text-yellow-600'
    case 4: return 'bg-blue-500 text-blue-600'
    case 5: return 'bg-green-500 text-green-600'
    default: return 'bg-red-500 text-red-600'
  }
})

const canSubmit = computed(() => {
  return form.current_password && 
         form.new_password && 
         form.confirm_password &&
         form.new_password === form.confirm_password &&
         form.new_password.length >= 6 &&
         passwordStrength.value >= 2 // 至少中等强度
})

const validatePassword = () => {
  error.value = ''
}

const handleSubmit = async () => {
  if (loading.value || !canSubmit.value) return
  
  if (form.new_password !== form.confirm_password) {
    error.value = '两次输入的密码不一致'
    return
  }
  
  if (passwordStrength.value < 2) {
    error.value = '密码强度太弱，请使用更复杂的密码'
    return
  }
  
  loading.value = true
  error.value = ''
  success.value = false
  
  try {
    await authStore.changePassword({
      current_password: form.current_password,
      new_password: form.new_password
    })
    
    success.value = true
    
    // 清空表单
    form.current_password = ''
    form.new_password = ''
    form.confirm_password = ''
    
    // 隐藏密码
    showCurrentPassword.value = false
    showNewPassword.value = false
    showConfirmPassword.value = false
    
    // 3秒后隐藏成功消息
    setTimeout(() => {
      success.value = false
    }, 3000)
    
    // 触发成功事件
    setTimeout(() => {
      // $emit('success')
    }, 1000)
    
  } catch (err: any) {
    error.value = err.message || '密码修改失败'
  } finally {
    loading.value = false
  }
}
</script>
