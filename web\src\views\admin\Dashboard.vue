<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">管理员仪表板</h1>
        <p class="mt-1 text-sm text-gray-600">
          系统概览和统计信息
        </p>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <UsersIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.total_users || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <CheckCircleIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.active_users || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <CubeIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">产品数量</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.total_products || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <KeyIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">总授权数</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.total_licenses || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 配额统计 -->
      <div class="grid grid-cols-1 gap-5 lg:grid-cols-2 mb-8">
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg leading-6 font-medium text-gray-900">配额分配情况</h3>
          </div>
          <div class="card-body">
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">已分配配额</span>
                <span class="text-sm font-medium text-gray-900">{{ stats?.total_quota_issued || 0 }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">已使用配额</span>
                <span class="text-sm font-medium text-gray-900">{{ stats?.total_quota_used || 0 }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">剩余配额</span>
                <span class="text-sm font-medium text-gray-900">{{ (stats?.total_quota_issued || 0) - (stats?.total_quota_used || 0) }}</span>
              </div>
              <div class="pt-2">
                <div class="mb-2 flex justify-between text-sm">
                  <span class="text-gray-600">使用率</span>
                  <span class="text-gray-600">{{ quotaUsagePercentage }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    :style="{ width: quotaUsagePercentage + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="text-lg leading-6 font-medium text-gray-900">授权状态分布</h3>
          </div>
          <div class="card-body">
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">活跃授权</span>
                <span class="text-sm font-medium text-green-600">{{ stats?.active_licenses || 0 }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">已撤销授权</span>
                <span class="text-sm font-medium text-red-600">{{ (stats?.total_licenses || 0) - (stats?.active_licenses || 0) }}</span>
              </div>
              <div class="pt-2">
                <div class="mb-2 flex justify-between text-sm">
                  <span class="text-gray-600">活跃率</span>
                  <span class="text-gray-600">{{ licenseActivePercentage }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-green-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: licenseActivePercentage + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 本月新增用户 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg leading-6 font-medium text-gray-900">本月统计</h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <div class="text-center">
              <div class="text-2xl font-bold text-primary-600">{{ stats?.new_users_this_month || 0 }}</div>
              <div class="text-sm text-gray-500">新注册用户</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{{ stats?.active_licenses || 0 }}</div>
              <div class="text-sm text-gray-500">活跃授权数</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ stats?.total_quota_used || 0 }}</div>
              <div class="text-sm text-gray-500">已使用配额</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Layout from '@/components/Layout.vue'
import api from '@/utils/api'
import type { AdminStats, ApiResponse } from '@/types'
import {
  UsersIcon,
  CheckCircleIcon,
  CubeIcon,
  KeyIcon,
} from '@heroicons/vue/24/outline'

const stats = ref<AdminStats | null>(null)

const quotaUsagePercentage = computed(() => {
  if (!stats.value || stats.value.total_quota_issued === 0) return 0
  return Math.round((stats.value.total_quota_used / stats.value.total_quota_issued) * 100)
})

const licenseActivePercentage = computed(() => {
  if (!stats.value || stats.value.total_licenses === 0) return 0
  return Math.round((stats.value.active_licenses / stats.value.total_licenses) * 100)
})

const fetchStats = async () => {
  try {
    const response = await api.get<ApiResponse<AdminStats>>('/admin/stats/overview')
    if (response.data.success) {
      stats.value = response.data.data!
    }
  } catch (error) {
    console.error('Failed to fetch admin stats:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>
