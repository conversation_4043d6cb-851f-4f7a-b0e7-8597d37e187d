export interface User {
  id: number
  email: string
  is_admin: boolean
  max_license_quota: number
  used_license_quota: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Product {
  id: number
  name: string
  description: string
  category: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface LicenseRequest {
  id: number
  user_id: number
  product_id: number
  machine_code: string
  license_code: string
  machine_name?: string
  company_name?: string
  contact_phone?: string
  os_type?: string
  install_time?: string
  remarks?: string
  status: string
  created_at: string
  updated_at: string
  user?: User
  product?: Product
}

export interface AdminLog {
  id: number
  admin_id: number
  target_user_id?: number
  action: string
  description: string
  old_value?: string
  new_value?: string
  ip_address: string
  created_at: string
  admin?: User
  target_user?: User
}

export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
}

export interface PaginationMeta {
  current_page: number
  per_page: number
  total: number
  total_pages: number
}

export interface PaginatedResponse<T = any> extends ApiResponse<T> {
  meta: PaginationMeta
}

export interface DashboardStats {
  remaining_quota: number
  used_quota: number
  total_quota: number
  total_licenses: number
  active_licenses: number
}

export interface AdminStats {
  total_users: number
  active_users: number
  total_products: number
  total_licenses: number
  active_licenses: number
  total_quota_issued: number
  total_quota_used: number
  new_users_this_month: number
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
}

export interface CreateUserRequest {
  email: string
  max_license_quota: number
  is_admin: boolean
}

export interface UpdateUserRequest {
  email: string
  is_admin: boolean
  is_active: boolean
}

export interface UpdateUserQuotaRequest {
  max_license_quota: number
  reason: string
}

export interface CreateProductRequest {
  name: string
  description: string
  category: string
}

export interface UpdateProductRequest {
  name: string
  description: string
  category: string
  is_active: boolean
}

export interface RequestLicenseRequest {
  product_id: number
  machine_code: string
  machine_name?: string
  company_name?: string
  contact_phone?: string
  os_type?: string
  install_time?: string
  remarks?: string
}

export interface ChangePasswordRequest {
  current_password: string
  new_password: string
}
