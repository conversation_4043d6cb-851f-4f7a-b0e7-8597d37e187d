<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 sm:mx-0 sm:h-10 sm:w-10">
              <PlusIcon class="h-6 w-6 text-primary-600" />
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                添加新产品
              </h3>
              <div class="mt-4">
                <form @submit.prevent="handleSubmit" class="space-y-4">
                  <div>
                    <label class="form-label">产品名称 *</label>
                    <input
                      v-model="form.name"
                      type="text"
                      required
                      class="form-input"
                      placeholder="请输入产品名称"
                    />
                  </div>

                  <div>
                    <label class="form-label">产品分类 *</label>
                    <select
                      v-model="form.category"
                      required
                      class="form-input"
                    >
                      <option value="">请选择分类</option>
                      <option value="企业软件">企业软件</option>
                      <option value="专业工具">专业工具</option>
                      <option value="标准应用">标准应用</option>
                      <option value="开发工具">开发工具</option>
                      <option value="系统软件">系统软件</option>
                      <option value="其他">其他</option>
                    </select>
                  </div>

                  <div>
                    <label class="form-label">产品描述</label>
                    <textarea
                      v-model="form.description"
                      rows="3"
                      class="form-input"
                      placeholder="请输入产品描述（可选）"
                    ></textarea>
                  </div>

                  <div v-if="error" class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">添加失败</h3>
                        <div class="mt-2 text-sm text-red-700">{{ error }}</div>
                      </div>
                    </div>
                  </div>

                  <div v-if="success" class="rounded-md bg-green-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <CheckCircleIcon class="h-5 w-5 text-green-400" />
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">添加成功</h3>
                        <div class="mt-2 text-sm text-green-700">
                          产品 "{{ form.name }}" 已成功添加到系统中
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            v-if="!success"
            @click="handleSubmit"
            :disabled="loading"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
          >
            <span v-if="loading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              添加中...
            </span>
            <span v-else>添加产品</span>
          </button>
          <button
            @click="$emit('close')"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            {{ success ? '关闭' : '取消' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import api from '@/utils/api'
import type { CreateProductRequest, ApiResponse } from '@/types'
import { 
  PlusIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon 
} from '@heroicons/vue/24/outline'

defineEmits<{
  close: []
  success: []
}>()

const loading = ref(false)
const error = ref('')
const success = ref(false)

const form = reactive<CreateProductRequest>({
  name: '',
  description: '',
  category: ''
})

const handleSubmit = async () => {
  if (loading.value) return
  
  loading.value = true
  error.value = ''
  
  try {
    const response = await api.post<ApiResponse>('/admin/products', form)
    
    if (response.data.success) {
      success.value = true
      
      // 2秒后自动触发成功事件
      setTimeout(() => {
        // $emit('success')
      }, 2000)
    } else {
      error.value = response.data.error || '添加失败'
    }
  } catch (err: any) {
    error.value = err.response?.data?.error || '添加失败，请检查输入信息'
  } finally {
    loading.value = false
  }
}
</script>
