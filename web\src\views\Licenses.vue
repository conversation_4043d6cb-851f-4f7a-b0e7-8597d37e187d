<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-900">我的授权记录</h1>
          <p class="mt-2 text-sm text-gray-700">
            查看和管理您申请的所有授权码
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <router-link
            to="/licenses/request"
            class="btn-primary"
          >
            申请新授权
          </router-link>
        </div>
      </div>

      <!-- 授权列表 -->
      <div class="mt-8 card">
        <div class="card-body">
          <div v-if="loading" class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>

          <div v-else-if="licenses.length === 0" class="text-center py-8">
            <KeyIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无授权记录</h3>
            <p class="mt-1 text-sm text-gray-500">您还没有申请过任何授权码</p>
            <div class="mt-6">
              <router-link
                to="/licenses/request"
                class="btn-primary"
              >
                申请第一个授权
              </router-link>
            </div>
          </div>

          <div v-else class="overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    产品信息
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    机器信息
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    授权码
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    申请时间
                  </th>
                  <th class="relative px-6 py-3">
                    <span class="sr-only">操作</span>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="license in licenses" :key="license.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ license.product?.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ license.product?.category }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ license.machine_name || '未命名' }}
                      </div>
                      <div class="text-sm text-gray-500 font-mono">
                        {{ formatMachineCode(license.machine_code) }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                        {{ license.license_code }}
                      </code>
                      <button
                        @click="copyToClipboard(license.license_code)"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        title="复制授权码"
                      >
                        <ClipboardIcon class="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        license.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      ]"
                    >
                      {{ license.status === 'active' ? '有效' : '已撤销' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(license.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      @click="viewDetails(license)"
                      class="text-primary-600 hover:text-primary-900"
                    >
                      查看详情
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- 分页 -->
            <div v-if="meta" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div class="flex-1 flex justify-between sm:hidden">
                <button
                  @click="previousPage"
                  :disabled="meta.current_page <= 1"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  @click="nextPage"
                  :disabled="meta.current_page >= meta.total_pages"
                  class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
              <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p class="text-sm text-gray-700">
                    显示第 <span class="font-medium">{{ (meta.current_page - 1) * meta.per_page + 1 }}</span>
                    到 <span class="font-medium">{{ Math.min(meta.current_page * meta.per_page, meta.total) }}</span>
                    条，共 <span class="font-medium">{{ meta.total }}</span> 条记录
                  </p>
                </div>
                <div>
                  <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      @click="previousPage"
                      :disabled="meta.current_page <= 1"
                      class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      @click="nextPage"
                      :disabled="meta.current_page >= meta.total_pages"
                      class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情模态框 -->
    <LicenseDetailModal
      v-if="selectedLicense"
      :license="selectedLicense"
      @close="selectedLicense = null"
    />
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Layout from '@/components/Layout.vue'
import LicenseDetailModal from '@/components/LicenseDetailModal.vue'
import api from '@/utils/api'
import type { LicenseRequest, PaginatedResponse, PaginationMeta } from '@/types'
import { KeyIcon, ClipboardIcon } from '@heroicons/vue/24/outline'

const loading = ref(false)
const licenses = ref<LicenseRequest[]>([])
const meta = ref<PaginationMeta | null>(null)
const selectedLicense = ref<LicenseRequest | null>(null)
const currentPage = ref(1)

const fetchLicenses = async (page = 1) => {
  loading.value = true
  try {
    const response = await api.get<PaginatedResponse<LicenseRequest[]>>('/licenses', {
      params: { page, per_page: 10 }
    })
    if (response.data.success) {
      licenses.value = response.data.data || []
      meta.value = response.data.meta
      currentPage.value = page
    }
  } catch (error) {
    console.error('Failed to fetch licenses:', error)
  } finally {
    loading.value = false
  }
}

const previousPage = () => {
  if (meta.value && currentPage.value > 1) {
    fetchLicenses(currentPage.value - 1)
  }
}

const nextPage = () => {
  if (meta.value && currentPage.value < meta.value.total_pages) {
    fetchLicenses(currentPage.value + 1)
  }
}

const formatMachineCode = (code: string) => {
  return code.replace(/(.{8})/g, '$1-').slice(0, -1)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // 这里可以添加一个toast提示
  } catch (error) {
    console.error('Failed to copy:', error)
  }
}

const viewDetails = (license: LicenseRequest) => {
  selectedLicense.value = license
}

onMounted(() => {
  fetchLicenses()
})
</script>
