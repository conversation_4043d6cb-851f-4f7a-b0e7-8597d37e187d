<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">操作日志</h1>
        <p class="mt-1 text-sm text-gray-600">查看管理员的操作记录和审计日志</p>
      </div>

      <!-- 筛选器 -->
      <div class="mb-6 card">
        <div class="card-body">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label class="form-label">操作类型</label>
              <select
                v-model="filters.action"
                @change="fetchLogs(1)"
                class="form-input"
              >
                <option value="">全部操作</option>
                <option value="CREATE_USER">创建用户</option>
                <option value="UPDATE_USER">更新用户</option>
                <option value="DELETE_USER">删除用户</option>
                <option value="UPDATE_QUOTA">更新配额</option>
                <option value="CREATE_PRODUCT">创建产品</option>
                <option value="UPDATE_PRODUCT">更新产品</option>
                <option value="DELETE_PRODUCT">删除产品</option>
                <option value="REVOKE_LICENSE">撤销授权</option>
              </select>
            </div>
            <div>
              <label class="form-label">管理员邮箱</label>
              <input
                v-model="filters.adminEmail"
                type="text"
                placeholder="搜索管理员邮箱"
                class="form-input"
                @input="debounceSearch"
              />
            </div>
            <div class="flex items-end">
              <button @click="resetFilters" class="btn-secondary w-full">
                重置筛选
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志列表 -->
      <div class="card">
        <div class="card-body">
          <div v-if="loading" class="text-center py-8">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"
            ></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>

          <div v-else-if="logs.length === 0" class="text-center py-8">
            <DocumentTextIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无操作日志</h3>
            <p class="mt-1 text-sm text-gray-500">系统中还没有任何操作记录</p>
          </div>

          <div v-else class="overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    操作时间
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    管理员
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    操作类型
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    操作描述
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    目标用户
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    IP地址
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="log in logs" :key="log.id">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ formatDate(log.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      {{ log.admin?.email }}
                    </div>
                    <div class="text-sm text-gray-500">
                      ID: {{ log.admin?.id }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        getActionColor(log.action),
                      ]"
                    >
                      {{ getActionText(log.action) }}
                    </span>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">
                      {{ log.description }}
                    </div>
                    <div
                      v-if="log.old_value || log.new_value"
                      class="text-xs text-gray-500 mt-1"
                    >
                      <span v-if="log.old_value"
                        >旧值: {{ log.old_value }}</span
                      >
                      <span v-if="log.old_value && log.new_value"> → </span>
                      <span v-if="log.new_value"
                        >新值: {{ log.new_value }}</span
                      >
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div v-if="log.target_user" class="text-sm text-gray-900">
                      {{ log.target_user.email }}
                    </div>
                    <div v-else class="text-sm text-gray-500">-</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ log.ip_address }}
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- 分页 -->
            <div
              v-if="meta"
              class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
            >
              <div class="flex-1 flex justify-between sm:hidden">
                <button
                  @click="previousPage"
                  :disabled="meta.current_page <= 1"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  @click="nextPage"
                  :disabled="meta.current_page >= meta.total_pages"
                  class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
              <div
                class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
              >
                <div>
                  <p class="text-sm text-gray-700">
                    显示第
                    <span class="font-medium">{{
                      (meta.current_page - 1) * meta.per_page + 1
                    }}</span>
                    到
                    <span class="font-medium">{{
                      Math.min(meta.current_page * meta.per_page, meta.total)
                    }}</span>
                    条，共
                    <span class="font-medium">{{ meta.total }}</span> 条记录
                  </p>
                </div>
                <div>
                  <nav
                    class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                  >
                    <button
                      @click="previousPage"
                      :disabled="meta.current_page <= 1"
                      class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      @click="nextPage"
                      :disabled="meta.current_page >= meta.total_pages"
                      class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import Layout from "@/components/Layout.vue";
import api from "@/utils/api";
import type { AdminLog, PaginatedResponse, PaginationMeta } from "@/types";
import { DocumentTextIcon } from "@heroicons/vue/24/outline";

const loading = ref(false);
const logs = ref<AdminLog[]>([]);
const meta = ref<PaginationMeta | null>(null);
const currentPage = ref(1);

const filters = reactive({
  action: "",
  adminEmail: "",
});

let searchTimeout: NodeJS.Timeout;

const fetchLogs = async (page = 1) => {
  loading.value = true;
  try {
    const params: any = { page, per_page: 20 };

    if (filters.action) params.action = filters.action;
    if (filters.adminEmail) params.admin_email = filters.adminEmail;

    const response = await api.get<PaginatedResponse<AdminLog[]>>(
      "/admin/logs",
      { params }
    );
    if (response.data.success) {
      logs.value = response.data.data || [];
      meta.value = response.data.meta;
      currentPage.value = page;
    }
  } catch (error) {
    console.error("Failed to fetch logs:", error);
  } finally {
    loading.value = false;
  }
};

const debounceSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    fetchLogs(1);
  }, 500);
};

const resetFilters = () => {
  filters.action = "";
  filters.adminEmail = "";
  fetchLogs(1);
};

const previousPage = () => {
  if (meta.value && currentPage.value > 1) {
    fetchLogs(currentPage.value - 1);
  }
};

const nextPage = () => {
  if (meta.value && currentPage.value < meta.value.total_pages) {
    fetchLogs(currentPage.value + 1);
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("zh-CN");
};

const getActionText = (action: string) => {
  const actionMap: Record<string, string> = {
    CREATE_USER: "创建用户",
    UPDATE_USER: "更新用户",
    DELETE_USER: "删除用户",
    UPDATE_QUOTA: "更新配额",
    CREATE_PRODUCT: "创建产品",
    UPDATE_PRODUCT: "更新产品",
    DELETE_PRODUCT: "删除产品",
    REVOKE_LICENSE: "撤销授权",
  };
  return actionMap[action] || action;
};

const getActionColor = (action: string) => {
  const colorMap: Record<string, string> = {
    CREATE_USER: "bg-green-100 text-green-800",
    UPDATE_USER: "bg-blue-100 text-blue-800",
    DELETE_USER: "bg-red-100 text-red-800",
    UPDATE_QUOTA: "bg-purple-100 text-purple-800",
    CREATE_PRODUCT: "bg-green-100 text-green-800",
    UPDATE_PRODUCT: "bg-blue-100 text-blue-800",
    DELETE_PRODUCT: "bg-red-100 text-red-800",
    REVOKE_LICENSE: "bg-orange-100 text-orange-800",
  };
  return colorMap[action] || "bg-gray-100 text-gray-800";
};

onMounted(() => {
  fetchLogs();
});
</script>
