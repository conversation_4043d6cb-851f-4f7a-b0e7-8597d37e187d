import{_ as $}from"./Layout.vue_vue_type_script_setup_true_lang-DaBFh0gi.js";import{c as C,b as s,o as q,d as S,r as A,k as j,l as D,m as N,p as P,n as V,g as o,h as l,t as a,s as k}from"./index-DgD6jlCj.js";import{r as Z}from"./CheckCircleIcon-CvQFFXss.js";import{r as z}from"./CubeIcon-9x93tKpn.js";import{r as E}from"./KeyIcon-CW4MztXx.js";function F(M,e){return q(),C("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"})])}const U={class:"px-4 py-6 sm:px-0"},G={class:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8"},H={class:"card"},I={class:"card-body"},J={class:"flex items-center"},K={class:"flex-shrink-0"},L={class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},O={class:"ml-5 w-0 flex-1"},Q={class:"text-lg font-medium text-gray-900"},R={class:"card"},T={class:"card-body"},W={class:"flex items-center"},X={class:"flex-shrink-0"},Y={class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},ss={class:"ml-5 w-0 flex-1"},ts={class:"text-lg font-medium text-gray-900"},es={class:"card"},as={class:"card-body"},ds={class:"flex items-center"},os={class:"flex-shrink-0"},ls={class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},is={class:"ml-5 w-0 flex-1"},ns={class:"text-lg font-medium text-gray-900"},cs={class:"card"},rs={class:"card-body"},_s={class:"flex items-center"},us={class:"flex-shrink-0"},xs={class:"w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center"},ms={class:"ml-5 w-0 flex-1"},vs={class:"text-lg font-medium text-gray-900"},fs={class:"grid grid-cols-1 gap-5 lg:grid-cols-2 mb-8"},gs={class:"card"},hs={class:"card-body"},ys={class:"space-y-4"},ps={class:"flex justify-between"},ws={class:"text-sm font-medium text-gray-900"},bs={class:"flex justify-between"},js={class:"text-sm font-medium text-gray-900"},ks={class:"flex justify-between"},qs={class:"text-sm font-medium text-gray-900"},Ms={class:"pt-2"},Bs={class:"mb-2 flex justify-between text-sm"},$s={class:"text-gray-600"},Cs={class:"w-full bg-gray-200 rounded-full h-2"},Ss={class:"card"},As={class:"card-body"},Ds={class:"space-y-4"},Ns={class:"flex justify-between"},Ps={class:"text-sm font-medium text-green-600"},Vs={class:"flex justify-between"},Zs={class:"text-sm font-medium text-red-600"},zs={class:"pt-2"},Es={class:"mb-2 flex justify-between text-sm"},Fs={class:"text-gray-600"},Us={class:"w-full bg-gray-200 rounded-full h-2"},Gs={class:"card"},Hs={class:"card-body"},Is={class:"grid grid-cols-1 gap-5 sm:grid-cols-3"},Js={class:"text-center"},Ks={class:"text-2xl font-bold text-primary-600"},Ls={class:"text-center"},Os={class:"text-2xl font-bold text-green-600"},Qs={class:"text-center"},Rs={class:"text-2xl font-bold text-blue-600"},tt=S({__name:"Dashboard",setup(M){const e=A(null),i=j(()=>!e.value||e.value.total_quota_issued===0?0:Math.round(e.value.total_quota_used/e.value.total_quota_issued*100)),n=j(()=>!e.value||e.value.total_licenses===0?0:Math.round(e.value.active_licenses/e.value.total_licenses*100)),B=async()=>{try{const d=await V.get("/admin/stats/overview");d.data.success&&(e.value=d.data.data)}catch(d){console.error("Failed to fetch admin stats:",d)}};return D(()=>{B()}),(d,t)=>(q(),N($,null,{default:P(()=>{var c,r,_,u,x,m,v,f,g,h,y,p,w,b;return[s("div",U,[t[17]||(t[17]=s("div",{class:"mb-6"},[s("h1",{class:"text-2xl font-bold text-gray-900"},"管理员仪表板"),s("p",{class:"mt-1 text-sm text-gray-600"}," 系统概览和统计信息 ")],-1)),s("div",G,[s("div",H,[s("div",I,[s("div",J,[s("div",K,[s("div",L,[o(l(F),{class:"w-5 h-5 text-white"})])]),s("div",O,[s("dl",null,[t[0]||(t[0]=s("dt",{class:"text-sm font-medium text-gray-500 truncate"},"总用户数",-1)),s("dd",Q,a(((c=e.value)==null?void 0:c.total_users)||0),1)])])])])]),s("div",R,[s("div",T,[s("div",W,[s("div",X,[s("div",Y,[o(l(Z),{class:"w-5 h-5 text-white"})])]),s("div",ss,[s("dl",null,[t[1]||(t[1]=s("dt",{class:"text-sm font-medium text-gray-500 truncate"},"活跃用户",-1)),s("dd",ts,a(((r=e.value)==null?void 0:r.active_users)||0),1)])])])])]),s("div",es,[s("div",as,[s("div",ds,[s("div",os,[s("div",ls,[o(l(z),{class:"w-5 h-5 text-white"})])]),s("div",is,[s("dl",null,[t[2]||(t[2]=s("dt",{class:"text-sm font-medium text-gray-500 truncate"},"产品数量",-1)),s("dd",ns,a(((_=e.value)==null?void 0:_.total_products)||0),1)])])])])]),s("div",cs,[s("div",rs,[s("div",_s,[s("div",us,[s("div",xs,[o(l(E),{class:"w-5 h-5 text-white"})])]),s("div",ms,[s("dl",null,[t[3]||(t[3]=s("dt",{class:"text-sm font-medium text-gray-500 truncate"},"总授权数",-1)),s("dd",vs,a(((u=e.value)==null?void 0:u.total_licenses)||0),1)])])])])])]),s("div",fs,[s("div",gs,[t[8]||(t[8]=s("div",{class:"card-header"},[s("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"配额分配情况")],-1)),s("div",hs,[s("div",ys,[s("div",ps,[t[4]||(t[4]=s("span",{class:"text-sm text-gray-600"},"已分配配额",-1)),s("span",ws,a(((x=e.value)==null?void 0:x.total_quota_issued)||0),1)]),s("div",bs,[t[5]||(t[5]=s("span",{class:"text-sm text-gray-600"},"已使用配额",-1)),s("span",js,a(((m=e.value)==null?void 0:m.total_quota_used)||0),1)]),s("div",ks,[t[6]||(t[6]=s("span",{class:"text-sm text-gray-600"},"剩余配额",-1)),s("span",qs,a((((v=e.value)==null?void 0:v.total_quota_issued)||0)-(((f=e.value)==null?void 0:f.total_quota_used)||0)),1)]),s("div",Ms,[s("div",Bs,[t[7]||(t[7]=s("span",{class:"text-gray-600"},"使用率",-1)),s("span",$s,a(i.value)+"%",1)]),s("div",Cs,[s("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:k({width:i.value+"%"})},null,4)])])])])]),s("div",Ss,[t[12]||(t[12]=s("div",{class:"card-header"},[s("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"授权状态分布")],-1)),s("div",As,[s("div",Ds,[s("div",Ns,[t[9]||(t[9]=s("span",{class:"text-sm text-gray-600"},"活跃授权",-1)),s("span",Ps,a(((g=e.value)==null?void 0:g.active_licenses)||0),1)]),s("div",Vs,[t[10]||(t[10]=s("span",{class:"text-sm text-gray-600"},"已撤销授权",-1)),s("span",Zs,a((((h=e.value)==null?void 0:h.total_licenses)||0)-(((y=e.value)==null?void 0:y.active_licenses)||0)),1)]),s("div",zs,[s("div",Es,[t[11]||(t[11]=s("span",{class:"text-gray-600"},"活跃率",-1)),s("span",Fs,a(n.value)+"%",1)]),s("div",Us,[s("div",{class:"bg-green-500 h-2 rounded-full transition-all duration-300",style:k({width:n.value+"%"})},null,4)])])])])])]),s("div",Gs,[t[16]||(t[16]=s("div",{class:"card-header"},[s("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"本月统计")],-1)),s("div",Hs,[s("div",Is,[s("div",Js,[s("div",Ks,a(((p=e.value)==null?void 0:p.new_users_this_month)||0),1),t[13]||(t[13]=s("div",{class:"text-sm text-gray-500"},"新注册用户",-1))]),s("div",Ls,[s("div",Os,a(((w=e.value)==null?void 0:w.active_licenses)||0),1),t[14]||(t[14]=s("div",{class:"text-sm text-gray-500"},"活跃授权数",-1))]),s("div",Qs,[s("div",Rs,a(((b=e.value)==null?void 0:b.total_quota_used)||0),1),t[15]||(t[15]=s("div",{class:"text-sm text-gray-500"},"已使用配额",-1))])])])])])]}),_:1}))}});export{tt as default};
