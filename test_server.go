package main

import (
	"fmt"
	"net/http"
)

func main() {
	fmt.Println("Starting test server on port 8080...")
	
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON><PERSON>().Set("Content-Type", "text/html")
		w.Write([]byte(`
<!DOCTYPE html>
<html>
<head>
    <title>License Manager Test</title>
</head>
<body>
    <h1>License Manager Test Server</h1>
    <p>Server is running successfully!</p>
</body>
</html>
		`))
	})
	
	fmt.Println("Server started successfully on http://localhost:8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		fmt.Printf("Server failed to start: %v\n", err)
	}
}
