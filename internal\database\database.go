package database

import (
	"fmt"
	"licmanager/internal/models"
	"licmanager/internal/utils"
	"os"
	"path/filepath"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Initialize(databasePath string) (*gorm.DB, error) {
	// 确保数据库目录存在
	dir := filepath.Dir(databasePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %v", err)
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(databasePath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	// 自动迁移
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %v", err)
	}

	return db, nil
}

func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.User{},
		&models.Product{},
		&models.LicenseRequest{},
		&models.AdminLog{},
		&models.SystemConfig{},
	)
}

// CreateDefaultAdmin 创建默认超级管理员
func CreateDefaultAdmin(db *gorm.DB) error {
	var count int64
	db.Model(&models.User{}).Where("is_admin = ?", true).Count(&count)

	if count > 0 {
		return nil // 已存在管理员
	}

	// 生成随机密码
	password := utils.GenerateRandomPassword(12)
	hashedPassword, err := utils.HashPassword(password)
	if err != nil {
		return err
	}

	admin := models.User{
		Email:    "<EMAIL>",
		Password: hashedPassword,
		IsAdmin:  true,
		IsActive: true,
	}

	if err := db.Create(&admin).Error; err != nil {
		return err
	}

	fmt.Printf("Default admin created:\n")
	fmt.Printf("Email: %s\n", admin.Email)
	fmt.Printf("Password: %s\n", password)
	fmt.Printf("Please change the password after first login!\n")

	// 创建默认产品
	if err := createDefaultProducts(db); err != nil {
		fmt.Printf("Warning: Failed to create default products: %v\n", err)
	}

	return nil
}

// createDefaultProducts 创建默认产品
func createDefaultProducts(db *gorm.DB) error {
	var count int64
	db.Model(&models.Product{}).Count(&count)

	if count > 0 {
		return nil // 已存在产品
	}

	products := []models.Product{
		{
			Name:        "企业版软件",
			Description: "功能完整的企业级软件解决方案",
			Category:    "企业软件",
			IsActive:    true,
		},
		{
			Name:        "专业版工具",
			Description: "面向专业用户的高级工具套件",
			Category:    "专业工具",
			IsActive:    true,
		},
		{
			Name:        "标准版应用",
			Description: "适合个人和小团队使用的标准应用",
			Category:    "标准应用",
			IsActive:    true,
		},
	}

	for _, product := range products {
		if err := db.Create(&product).Error; err != nil {
			return err
		}
	}

	fmt.Printf("Default products created successfully\n")
	return nil
}
