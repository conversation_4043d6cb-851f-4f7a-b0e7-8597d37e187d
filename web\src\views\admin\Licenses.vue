<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">授权管理</h1>
        <p class="mt-1 text-sm text-gray-600">查看和管理所有用户的授权记录</p>
      </div>

      <!-- 筛选器 -->
      <div class="mb-6 card">
        <div class="card-body">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
            <div>
              <label class="form-label">状态筛选</label>
              <select
                v-model="filters.status"
                @change="fetchLicenses(1)"
                class="form-input"
              >
                <option value="">全部状态</option>
                <option value="active">有效</option>
                <option value="revoked">已撤销</option>
              </select>
            </div>
            <div>
              <label class="form-label">用户邮箱</label>
              <input
                v-model="filters.userEmail"
                type="text"
                placeholder="搜索用户邮箱"
                class="form-input"
                @input="debounceSearch"
              />
            </div>
            <div>
              <label class="form-label">产品名称</label>
              <input
                v-model="filters.productName"
                type="text"
                placeholder="搜索产品名称"
                class="form-input"
                @input="debounceSearch"
              />
            </div>
            <div class="flex items-end">
              <button @click="resetFilters" class="btn-secondary w-full">
                重置筛选
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 授权列表 -->
      <div class="card">
        <div class="card-body">
          <div v-if="loading" class="text-center py-8">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"
            ></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>

          <div v-else-if="licenses.length === 0" class="text-center py-8">
            <KeyIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无授权记录</h3>
            <p class="mt-1 text-sm text-gray-500">系统中还没有任何授权记录</p>
          </div>

          <div v-else class="overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    用户信息
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    产品信息
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    机器信息
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    授权码
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    状态
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    申请时间
                  </th>
                  <th class="relative px-6 py-3">
                    <span class="sr-only">操作</span>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="license in licenses" :key="license.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ license.user?.email }}
                      </div>
                      <div class="text-sm text-gray-500">
                        ID: {{ license.user?.id }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ license.product?.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ license.product?.category }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ license.machine_name || "未命名" }}
                      </div>
                      <div class="text-sm text-gray-500 font-mono">
                        {{ formatMachineCode(license.machine_code) }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <code
                        class="text-sm font-mono bg-gray-100 px-2 py-1 rounded"
                      >
                        {{ license.license_code }}
                      </code>
                      <button
                        @click="copyToClipboard(license.license_code)"
                        class="ml-2 text-gray-400 hover:text-gray-600"
                        title="复制授权码"
                      >
                        <ClipboardIcon class="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        license.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800',
                      ]"
                    >
                      {{ license.status === "active" ? "有效" : "已撤销" }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(license.created_at) }}
                  </td>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                  >
                    <div class="flex space-x-2">
                      <button
                        @click="viewDetails(license)"
                        class="text-primary-600 hover:text-primary-900"
                      >
                        详情
                      </button>
                      <button
                        v-if="license.status === 'active'"
                        @click="revokeLicense(license)"
                        class="text-red-600 hover:text-red-900"
                      >
                        撤销
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- 分页 -->
            <div
              v-if="meta"
              class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
            >
              <div class="flex-1 flex justify-between sm:hidden">
                <button
                  @click="previousPage"
                  :disabled="meta.current_page <= 1"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  @click="nextPage"
                  :disabled="meta.current_page >= meta.total_pages"
                  class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
              <div
                class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
              >
                <div>
                  <p class="text-sm text-gray-700">
                    显示第
                    <span class="font-medium">{{
                      (meta.current_page - 1) * meta.per_page + 1
                    }}</span>
                    到
                    <span class="font-medium">{{
                      Math.min(meta.current_page * meta.per_page, meta.total)
                    }}</span>
                    条，共
                    <span class="font-medium">{{ meta.total }}</span> 条记录
                  </p>
                </div>
                <div>
                  <nav
                    class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                  >
                    <button
                      @click="previousPage"
                      :disabled="meta.current_page <= 1"
                      class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      @click="nextPage"
                      :disabled="meta.current_page >= meta.total_pages"
                      class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情模态框 -->
    <LicenseDetailModal
      v-if="selectedLicense"
      :license="selectedLicense"
      @close="selectedLicense = null"
    />
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import Layout from "@/components/Layout.vue";
import LicenseDetailModal from "@/components/LicenseDetailModal.vue";
import api from "@/utils/api";
import type {
  LicenseRequest,
  PaginatedResponse,
  PaginationMeta,
  ApiResponse,
} from "@/types";
import { KeyIcon, ClipboardIcon } from "@heroicons/vue/24/outline";

const loading = ref(false);
const licenses = ref<LicenseRequest[]>([]);
const meta = ref<PaginationMeta | null>(null);
const currentPage = ref(1);
const selectedLicense = ref<LicenseRequest | null>(null);

const filters = reactive({
  status: "",
  userEmail: "",
  productName: "",
});

let searchTimeout: NodeJS.Timeout;

const fetchLicenses = async (page = 1) => {
  loading.value = true;
  try {
    const params: any = { page, per_page: 10 };

    if (filters.status) params.status = filters.status;
    if (filters.userEmail) params.user_email = filters.userEmail;
    if (filters.productName) params.product_name = filters.productName;

    const response = await api.get<PaginatedResponse<LicenseRequest[]>>(
      "/admin/licenses",
      { params }
    );
    if (response.data.success) {
      licenses.value = response.data.data || [];
      meta.value = response.data.meta;
      currentPage.value = page;
    }
  } catch (error) {
    console.error("Failed to fetch licenses:", error);
  } finally {
    loading.value = false;
  }
};

const debounceSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    fetchLicenses(1);
  }, 500);
};

const resetFilters = () => {
  filters.status = "";
  filters.userEmail = "";
  filters.productName = "";
  fetchLicenses(1);
};

const previousPage = () => {
  if (meta.value && currentPage.value > 1) {
    fetchLicenses(currentPage.value - 1);
  }
};

const nextPage = () => {
  if (meta.value && currentPage.value < meta.value.total_pages) {
    fetchLicenses(currentPage.value + 1);
  }
};

const formatMachineCode = (code: string) => {
  return code.replace(/(.{8})/g, "$1-").slice(0, -1);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("zh-CN");
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    // 这里可以添加一个toast提示
  } catch (error) {
    console.error("Failed to copy:", error);
  }
};

const viewDetails = (license: LicenseRequest) => {
  selectedLicense.value = license;
};

const revokeLicense = async (license: LicenseRequest) => {
  if (
    !confirm(`确定要撤销授权码 ${license.license_code} 吗？此操作不可恢复。`)
  ) {
    return;
  }

  try {
    const response = await api.put<ApiResponse>(
      `/admin/licenses/${license.id}/revoke`
    );
    if (response.data.success) {
      await fetchLicenses(currentPage.value);
    } else {
      alert(response.data.error || "撤销失败");
    }
  } catch (error: any) {
    alert(error.response?.data?.error || "撤销失败");
  }
};

onMounted(() => {
  fetchLicenses();
});
</script>
