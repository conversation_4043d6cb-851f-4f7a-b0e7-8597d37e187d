import{_ as V}from"./Layout.vue_vue_type_script_setup_true_lang-DaBFh0gi.js";import{r as D}from"./KeyIcon-CW4MztXx.js";import{r as L}from"./ClipboardIcon-UcrMZxcD.js";import{d as j,c as i,o as n,b as e,g as x,h as b,e as m,t as a,x as N,r as v,l as q,m as C,p as h,n as P,q as S,i as u,F as E,y as A}from"./index-DgD6jlCj.js";const G={class:"fixed inset-0 z-50 overflow-y-auto"},H={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},I={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},J={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},K={class:"sm:flex sm:items-start"},O={class:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 sm:mx-0 sm:h-10 sm:w-10"},Q={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full"},R={class:"mt-4 space-y-4"},U={class:"mt-1"},W={class:"text-sm text-gray-900"},X={class:"text-sm text-gray-500"},Y={class:"text-sm text-gray-500"},Z={class:"mt-1 flex items-center"},ee={class:"flex-1 text-sm font-mono bg-gray-100 px-3 py-2 rounded border"},te={class:"mt-1"},se={class:"block text-sm font-mono bg-gray-100 px-3 py-2 rounded border"},oe={key:0},ae={class:"mt-1 text-sm text-gray-900"},le={key:1},ne={class:"mt-1 text-sm text-gray-900"},ie={key:2},re={class:"mt-1 text-sm text-gray-900"},de={key:3},me={class:"mt-1 text-sm text-gray-900"},ce={key:4},pe={class:"mt-1 text-sm text-gray-900"},ue={key:5},xe={class:"mt-1 text-sm text-gray-900"},ye={class:"mt-1 text-sm text-gray-900"},ge={class:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},fe=j({__name:"LicenseDetailModal",props:{license:{}},emits:["close"],setup(z){const y=s=>s.replace(/(.{8})/g,"$1-").slice(0,-1),c=s=>new Date(s).toLocaleString("zh-CN"),l=async s=>{try{await navigator.clipboard.writeText(s)}catch(t){console.error("Failed to copy:",t)}};return(s,t)=>{var p,g,f;return n(),i("div",G,[e("div",H,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:t[0]||(t[0]=_=>s.$emit("close"))}),t[15]||(t[15]=e("span",{class:"hidden sm:inline-block sm:align-middle sm:h-screen"},"​",-1)),e("div",I,[e("div",J,[e("div",K,[e("div",O,[x(b(D),{class:"h-6 w-6 text-primary-600"})]),e("div",Q,[t[14]||(t[14]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900"}," 授权详情 ",-1)),e("div",R,[e("div",null,[t[3]||(t[3]=e("label",{class:"block text-sm font-medium text-gray-700"},"产品信息",-1)),e("div",U,[e("p",W,a((p=s.license.product)==null?void 0:p.name),1),e("p",X,a((g=s.license.product)==null?void 0:g.description),1),e("p",Y,"分类："+a((f=s.license.product)==null?void 0:f.category),1)])]),e("div",null,[t[4]||(t[4]=e("label",{class:"block text-sm font-medium text-gray-700"},"授权码",-1)),e("div",Z,[e("code",ee,a(s.license.license_code),1),e("button",{onClick:t[1]||(t[1]=_=>l(s.license.license_code)),class:"ml-2 p-2 text-gray-400 hover:text-gray-600 rounded",title:"复制授权码"},[x(b(L),{class:"h-4 w-4"})])])]),e("div",null,[t[5]||(t[5]=e("label",{class:"block text-sm font-medium text-gray-700"},"机器码",-1)),e("div",te,[e("code",se,a(y(s.license.machine_code)),1)])]),s.license.machine_name?(n(),i("div",oe,[t[6]||(t[6]=e("label",{class:"block text-sm font-medium text-gray-700"},"机器名称",-1)),e("p",ae,a(s.license.machine_name),1)])):m("",!0),s.license.company_name?(n(),i("div",le,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-700"},"单位名称",-1)),e("p",ne,a(s.license.company_name),1)])):m("",!0),s.license.contact_phone?(n(),i("div",ie,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700"},"联系电话",-1)),e("p",re,a(s.license.contact_phone),1)])):m("",!0),s.license.os_type?(n(),i("div",de,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700"},"操作系统",-1)),e("p",me,a(s.license.os_type),1)])):m("",!0),s.license.install_time?(n(),i("div",ce,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700"},"安装时间",-1)),e("p",pe,a(c(s.license.install_time)),1)])):m("",!0),s.license.remarks?(n(),i("div",ue,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700"},"备注信息",-1)),e("p",xe,a(s.license.remarks),1)])):m("",!0),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700"},"状态",-1)),e("span",{class:N(["inline-flex mt-1 px-2 py-1 text-xs font-semibold rounded-full",s.license.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},a(s.license.status==="active"?"有效":"已撤销"),3)]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700"},"申请时间",-1)),e("p",ye,a(c(s.license.created_at)),1)])])])])]),e("div",ge,[e("button",{onClick:t[2]||(t[2]=_=>s.$emit("close")),class:"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," 关闭 ")])])])])}}}),ve={class:"px-4 py-6 sm:px-0"},_e={class:"sm:flex sm:items-center"},be={class:"mt-4 sm:mt-0 sm:ml-16 sm:flex-none"},he={class:"mt-8 card"},we={class:"card-body"},ke={key:0,class:"text-center py-8"},$e={key:1,class:"text-center py-8"},Ce={class:"mt-6"},De={key:2,class:"overflow-hidden"},Le={class:"min-w-full divide-y divide-gray-200"},je={class:"bg-white divide-y divide-gray-200"},Ne={class:"px-6 py-4 whitespace-nowrap"},ze={class:"text-sm font-medium text-gray-900"},Fe={class:"text-sm text-gray-500"},Me={class:"px-6 py-4 whitespace-nowrap"},Te={class:"text-sm font-medium text-gray-900"},Be={class:"text-sm text-gray-500 font-mono"},Ve={class:"px-6 py-4 whitespace-nowrap"},qe={class:"flex items-center"},Pe={class:"text-sm font-mono bg-gray-100 px-2 py-1 rounded"},Se=["onClick"],Ee={class:"px-6 py-4 whitespace-nowrap"},Ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ge={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},He=["onClick"],Ie={key:0,class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},Je={class:"flex-1 flex justify-between sm:hidden"},Ke=["disabled"],Oe=["disabled"],Qe={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Re={class:"text-sm text-gray-700"},Ue={class:"font-medium"},We={class:"font-medium"},Xe={class:"font-medium"},Ye={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Ze=["disabled"],et=["disabled"],lt=j({__name:"Licenses",setup(z){const y=v(!1),c=v([]),l=v(null),s=v(null),t=v(1),p=async(d=1)=>{y.value=!0;try{const o=await P.get("/licenses",{params:{page:d,per_page:10}});o.data.success&&(c.value=o.data.data||[],l.value=o.data.meta,t.value=d)}catch(o){console.error("Failed to fetch licenses:",o)}finally{y.value=!1}},g=()=>{l.value&&t.value>1&&p(t.value-1)},f=()=>{l.value&&t.value<l.value.total_pages&&p(t.value+1)},_=d=>d.replace(/(.{8})/g,"$1-").slice(0,-1),F=d=>new Date(d).toLocaleString("zh-CN"),M=async d=>{try{await navigator.clipboard.writeText(d)}catch(o){console.error("Failed to copy:",o)}},T=d=>{s.value=d};return q(()=>{p()}),(d,o)=>{const w=S("router-link");return n(),C(V,null,{default:h(()=>[e("div",ve,[e("div",_e,[o[2]||(o[2]=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-bold text-gray-900"},"我的授权记录"),e("p",{class:"mt-2 text-sm text-gray-700"}," 查看和管理您申请的所有授权码 ")],-1)),e("div",be,[x(w,{to:"/licenses/request",class:"btn-primary"},{default:h(()=>o[1]||(o[1]=[u(" 申请新授权 ")])),_:1,__:[1]})])]),e("div",he,[e("div",we,[y.value?(n(),i("div",ke,o[3]||(o[3]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"加载中...",-1)]))):c.value.length===0?(n(),i("div",$e,[x(b(D),{class:"mx-auto h-12 w-12 text-gray-400"}),o[5]||(o[5]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"暂无授权记录",-1)),o[6]||(o[6]=e("p",{class:"mt-1 text-sm text-gray-500"},"您还没有申请过任何授权码",-1)),e("div",Ce,[x(w,{to:"/licenses/request",class:"btn-primary"},{default:h(()=>o[4]||(o[4]=[u(" 申请第一个授权 ")])),_:1,__:[4]})])])):(n(),i("div",De,[e("table",Le,[o[7]||(o[7]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 产品信息 "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 机器信息 "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 授权码 "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 状态 "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 申请时间 "),e("th",{class:"relative px-6 py-3"},[e("span",{class:"sr-only"},"操作")])])],-1)),e("tbody",je,[(n(!0),i(E,null,A(c.value,r=>{var k,$;return n(),i("tr",{key:r.id},[e("td",Ne,[e("div",null,[e("div",ze,a((k=r.product)==null?void 0:k.name),1),e("div",Fe,a(($=r.product)==null?void 0:$.category),1)])]),e("td",Me,[e("div",null,[e("div",Te,a(r.machine_name||"未命名"),1),e("div",Be,a(_(r.machine_code)),1)])]),e("td",Ve,[e("div",qe,[e("code",Pe,a(r.license_code),1),e("button",{onClick:B=>M(r.license_code),class:"ml-2 text-gray-400 hover:text-gray-600",title:"复制授权码"},[x(b(L),{class:"h-4 w-4"})],8,Se)])]),e("td",Ee,[e("span",{class:N(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",r.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},a(r.status==="active"?"有效":"已撤销"),3)]),e("td",Ae,a(F(r.created_at)),1),e("td",Ge,[e("button",{onClick:B=>T(r),class:"text-primary-600 hover:text-primary-900"}," 查看详情 ",8,He)])])}),128))])]),l.value?(n(),i("div",Ie,[e("div",Je,[e("button",{onClick:g,disabled:l.value.current_page<=1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," 上一页 ",8,Ke),e("button",{onClick:f,disabled:l.value.current_page>=l.value.total_pages,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," 下一页 ",8,Oe)]),e("div",Qe,[e("div",null,[e("p",Re,[o[8]||(o[8]=u(" 显示第 ")),e("span",Ue,a((l.value.current_page-1)*l.value.per_page+1),1),o[9]||(o[9]=u(" 到 ")),e("span",We,a(Math.min(l.value.current_page*l.value.per_page,l.value.total)),1),o[10]||(o[10]=u(" 条，共 ")),e("span",Xe,a(l.value.total),1),o[11]||(o[11]=u(" 条记录 "))])]),e("div",null,[e("nav",Ye,[e("button",{onClick:g,disabled:l.value.current_page<=1,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," 上一页 ",8,Ze),e("button",{onClick:f,disabled:l.value.current_page>=l.value.total_pages,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," 下一页 ",8,et)])])])])):m("",!0)]))])])]),s.value?(n(),C(fe,{key:0,license:s.value,onClose:o[0]||(o[0]=r=>s.value=null)},null,8,["license"])):m("",!0)]),_:1})}}});export{lt as default};
