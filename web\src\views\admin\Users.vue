<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
          <p class="mt-2 text-sm text-gray-700">
            管理系统中的所有用户账户，包括创建、编辑和配额管理
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button @click="showCreateModal = true" class="btn-primary">
            创建用户
          </button>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="mt-8 card">
        <div class="card-body">
          <div v-if="loading" class="text-center py-8">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"
            ></div>
            <p class="mt-2 text-sm text-gray-500">加载中...</p>
          </div>

          <div v-else-if="users.length === 0" class="text-center py-8">
            <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无用户</h3>
            <p class="mt-1 text-sm text-gray-500">开始创建第一个用户</p>
            <div class="mt-6">
              <button @click="showCreateModal = true" class="btn-primary">
                创建用户
              </button>
            </div>
          </div>

          <div v-else class="overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    用户信息
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    角色
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    配额使用
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    状态
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    注册时间
                  </th>
                  <th class="relative px-6 py-3">
                    <span class="sr-only">操作</span>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="user in users" :key="user.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div
                          class="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center"
                        >
                          <span class="text-white text-sm font-medium">
                            {{ user.email.charAt(0).toUpperCase() }}
                          </span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          {{ user.email }}
                        </div>
                        <div class="text-sm text-gray-500">
                          ID: {{ user.id }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        user.is_admin
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-blue-100 text-blue-800',
                      ]"
                    >
                      {{ user.is_admin ? "管理员" : "普通用户" }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {{ user.used_license_quota }} /
                      {{ user.max_license_quota }}
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        class="bg-primary-600 h-2 rounded-full"
                        :style="{ width: getQuotaPercentage(user) + '%' }"
                      ></div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span
                      :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        user.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800',
                      ]"
                    >
                      {{ user.is_active ? "正常" : "禁用" }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(user.created_at) }}
                  </td>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                  >
                    <div class="flex space-x-2">
                      <button
                        @click="editUser(user)"
                        class="text-primary-600 hover:text-primary-900"
                      >
                        编辑
                      </button>
                      <button
                        @click="editQuota(user)"
                        class="text-green-600 hover:text-green-900"
                      >
                        配额
                      </button>
                      <button
                        v-if="!user.is_admin"
                        @click="deleteUser(user)"
                        class="text-red-600 hover:text-red-900"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- 分页 -->
            <div
              v-if="meta"
              class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
            >
              <div class="flex-1 flex justify-between sm:hidden">
                <button
                  @click="previousPage"
                  :disabled="meta.current_page <= 1"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  @click="nextPage"
                  :disabled="meta.current_page >= meta.total_pages"
                  class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
              <div
                class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"
              >
                <div>
                  <p class="text-sm text-gray-700">
                    显示第
                    <span class="font-medium">{{
                      (meta.current_page - 1) * meta.per_page + 1
                    }}</span>
                    到
                    <span class="font-medium">{{
                      Math.min(meta.current_page * meta.per_page, meta.total)
                    }}</span>
                    条，共
                    <span class="font-medium">{{ meta.total }}</span> 条记录
                  </p>
                </div>
                <div>
                  <nav
                    class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                  >
                    <button
                      @click="previousPage"
                      :disabled="meta.current_page <= 1"
                      class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      @click="nextPage"
                      :disabled="meta.current_page >= meta.total_pages"
                      class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建用户模态框 -->
    <CreateUserModal
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @success="handleCreateSuccess"
    />

    <!-- 编辑用户模态框 -->
    <EditUserModal
      v-if="showEditModal && selectedUser"
      :user="selectedUser"
      @close="showEditModal = false"
      @success="handleEditSuccess"
    />

    <!-- 编辑配额模态框 -->
    <EditQuotaModal
      v-if="showQuotaModal && selectedUser"
      :user="selectedUser"
      @close="showQuotaModal = false"
      @success="handleQuotaSuccess"
    />
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import Layout from "@/components/Layout.vue";
import CreateUserModal from "@/components/admin/CreateUserModal.vue";
import EditUserModal from "@/components/admin/EditUserModal.vue";
import EditQuotaModal from "@/components/admin/EditQuotaModal.vue";
import api from "@/utils/api";
import type {
  User,
  PaginatedResponse,
  PaginationMeta,
  ApiResponse,
} from "@/types";
import { UsersIcon } from "@heroicons/vue/24/outline";

const loading = ref(false);
const users = ref<User[]>([]);
const meta = ref<PaginationMeta | null>(null);
const currentPage = ref(1);

const showCreateModal = ref(false);
const showEditModal = ref(false);
const showQuotaModal = ref(false);
const selectedUser = ref<User | null>(null);

const fetchUsers = async (page = 1) => {
  loading.value = true;
  try {
    const response = await api.get<PaginatedResponse<User[]>>("/admin/users", {
      params: { page, per_page: 10 },
    });
    if (response.data.success) {
      users.value = response.data.data || [];
      meta.value = response.data.meta;
      currentPage.value = page;
    }
  } catch (error) {
    console.error("Failed to fetch users:", error);
  } finally {
    loading.value = false;
  }
};

const previousPage = () => {
  if (meta.value && currentPage.value > 1) {
    fetchUsers(currentPage.value - 1);
  }
};

const nextPage = () => {
  if (meta.value && currentPage.value < meta.value.total_pages) {
    fetchUsers(currentPage.value + 1);
  }
};

const getQuotaPercentage = (user: User) => {
  if (user.max_license_quota === 0) return 0;
  return Math.round((user.used_license_quota / user.max_license_quota) * 100);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("zh-CN");
};

const editUser = (user: User) => {
  selectedUser.value = user;
  showEditModal.value = true;
};

const editQuota = (user: User) => {
  selectedUser.value = user;
  showQuotaModal.value = true;
};

const deleteUser = async (user: User) => {
  if (!confirm(`确定要删除用户 ${user.email} 吗？此操作不可恢复。`)) {
    return;
  }

  try {
    const response = await api.delete<ApiResponse>(`/admin/users/${user.id}`);
    if (response.data.success) {
      await fetchUsers(currentPage.value);
    } else {
      alert(response.data.error || "删除失败");
    }
  } catch (error: any) {
    alert(error.response?.data?.error || "删除失败");
  }
};

const handleCreateSuccess = () => {
  showCreateModal.value = false;
  fetchUsers(currentPage.value);
};

const handleEditSuccess = () => {
  showEditModal.value = false;
  selectedUser.value = null;
  fetchUsers(currentPage.value);
};

const handleQuotaSuccess = () => {
  showQuotaModal.value = false;
  selectedUser.value = null;
  fetchUsers(currentPage.value);
};

onMounted(() => {
  fetchUsers();
});
</script>
