<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="max-w-2xl mx-auto">
        <div class="mb-6">
          <h1 class="text-2xl font-bold text-gray-900">申请授权码</h1>
          <p class="mt-1 text-sm text-gray-600">
            为您的产品申请新的授权码
          </p>
        </div>

        <div class="card">
          <div class="card-body">
            <form @submit.prevent="handleSubmit" class="space-y-6">
              <!-- 产品选择 -->
              <div>
                <label class="form-label">选择产品 *</label>
                <select
                  v-model="form.product_id"
                  required
                  class="form-input"
                >
                  <option value="">请选择产品</option>
                  <option
                    v-for="product in products"
                    :key="product.id"
                    :value="product.id"
                  >
                    {{ product.name }} - {{ product.category }}
                  </option>
                </select>
              </div>

              <!-- 机器码 -->
              <div>
                <label class="form-label">机器码 *</label>
                <input
                  v-model="form.machine_code"
                  type="text"
                  required
                  placeholder="请输入32位十六进制机器码"
                  pattern="[0-9A-Fa-f]{32}"
                  maxlength="32"
                  class="form-input font-mono"
                />
                <p class="mt-1 text-sm text-gray-500">
                  机器码应为32位十六进制字符串，由您的软件生成
                </p>
              </div>

              <!-- 可选信息 -->
              <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">可选信息</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label class="form-label">机器名称</label>
                    <input
                      v-model="form.machine_name"
                      type="text"
                      placeholder="例如：办公室电脑"
                      class="form-input"
                    />
                  </div>

                  <div>
                    <label class="form-label">单位名称</label>
                    <input
                      v-model="form.company_name"
                      type="text"
                      placeholder="例如：某某公司"
                      class="form-input"
                    />
                  </div>

                  <div>
                    <label class="form-label">联系电话</label>
                    <input
                      v-model="form.contact_phone"
                      type="tel"
                      placeholder="例如：13800138000"
                      class="form-input"
                    />
                  </div>

                  <div>
                    <label class="form-label">操作系统</label>
                    <select v-model="form.os_type" class="form-input">
                      <option value="">请选择</option>
                      <option value="Windows 11">Windows 11</option>
                      <option value="Windows 10">Windows 10</option>
                      <option value="Windows Server 2022">Windows Server 2022</option>
                      <option value="Windows Server 2019">Windows Server 2019</option>
                      <option value="macOS">macOS</option>
                      <option value="Ubuntu">Ubuntu</option>
                      <option value="CentOS">CentOS</option>
                      <option value="其他">其他</option>
                    </select>
                  </div>

                  <div class="sm:col-span-2">
                    <label class="form-label">安装时间</label>
                    <input
                      v-model="form.install_time"
                      type="datetime-local"
                      class="form-input"
                    />
                  </div>

                  <div class="sm:col-span-2">
                    <label class="form-label">备注信息</label>
                    <textarea
                      v-model="form.remarks"
                      rows="3"
                      placeholder="其他需要说明的信息..."
                      class="form-input"
                    ></textarea>
                  </div>
                </div>
              </div>

              <!-- 错误提示 -->
              <div v-if="error" class="rounded-md bg-red-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                      申请失败
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                      {{ error }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 成功提示 -->
              <div v-if="success" class="rounded-md bg-green-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <CheckCircleIcon class="h-5 w-5 text-green-400" />
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">
                      申请成功
                    </h3>
                    <div class="mt-2 text-sm text-green-700">
                      <p>您的授权码已生成：</p>
                      <div class="mt-2 flex items-center">
                        <code class="bg-white px-2 py-1 rounded border font-mono">
                          {{ generatedLicense?.license_code }}
                        </code>
                        <button
                          @click="copyToClipboard(generatedLicense?.license_code || '')"
                          class="ml-2 text-green-600 hover:text-green-800"
                          title="复制授权码"
                        >
                          <ClipboardIcon class="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 提交按钮 -->
              <div class="flex justify-end space-x-3">
                <router-link
                  to="/licenses"
                  class="btn-secondary"
                >
                  返回列表
                </router-link>
                <button
                  type="submit"
                  :disabled="loading"
                  class="btn-primary disabled:opacity-50"
                >
                  <span v-if="loading" class="flex items-center">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    申请中...
                  </span>
                  <span v-else>申请授权码</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '@/components/Layout.vue'
import api from '@/utils/api'
import type { Product, LicenseRequest, ApiResponse, RequestLicenseRequest } from '@/types'
import { ExclamationTriangleIcon, CheckCircleIcon, ClipboardIcon } from '@heroicons/vue/24/outline'

const router = useRouter()
const loading = ref(false)
const error = ref('')
const success = ref(false)
const products = ref<Product[]>([])
const generatedLicense = ref<LicenseRequest | null>(null)

const form = reactive<RequestLicenseRequest>({
  product_id: 0,
  machine_code: '',
  machine_name: '',
  company_name: '',
  contact_phone: '',
  os_type: '',
  install_time: '',
  remarks: ''
})

const fetchProducts = async () => {
  try {
    const response = await api.get<ApiResponse<Product[]>>('/products')
    if (response.data.success) {
      products.value = response.data.data || []
    }
  } catch (error) {
    console.error('Failed to fetch products:', error)
  }
}

const handleSubmit = async () => {
  if (loading.value) return
  
  loading.value = true
  error.value = ''
  success.value = false
  
  try {
    // 处理安装时间
    const submitData = { ...form }
    if (submitData.install_time) {
      submitData.install_time = new Date(submitData.install_time).toISOString()
    } else {
      delete submitData.install_time
    }
    
    const response = await api.post<ApiResponse<LicenseRequest>>('/licenses/request', submitData)
    
    if (response.data.success) {
      success.value = true
      generatedLicense.value = response.data.data!
      
      // 3秒后自动跳转到授权列表
      setTimeout(() => {
        router.push('/licenses')
      }, 3000)
    } else {
      error.value = response.data.error || '申请失败'
    }
  } catch (err: any) {
    error.value = err.response?.data?.error || '申请失败，请检查输入信息'
  } finally {
    loading.value = false
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // 这里可以添加一个toast提示
  } catch (error) {
    console.error('Failed to copy:', error)
  }
}

onMounted(() => {
  fetchProducts()
})
</script>
