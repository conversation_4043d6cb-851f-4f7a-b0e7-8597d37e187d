<template>
  <Layout>
    <div class="px-4 py-6 sm:px-0">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">仪表板</h1>
        <p class="mt-1 text-sm text-gray-600">
          欢迎回来，{{ authStore.user?.email }}
        </p>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <CheckCircleIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">剩余配额</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.remaining_quota || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <DocumentTextIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">已用配额</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.used_quota || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <CubeIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">总配额</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.total_quota || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <KeyIcon class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">活跃授权</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats?.active_licenses || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 配额使用进度 -->
      <div class="card mb-8">
        <div class="card-header">
          <h3 class="text-lg leading-6 font-medium text-gray-900">配额使用情况</h3>
        </div>
        <div class="card-body">
          <div class="mb-2 flex justify-between text-sm">
            <span class="text-gray-600">已使用 {{ stats?.used_quota || 0 }} / {{ stats?.total_quota || 0 }}</span>
            <span class="text-gray-600">{{ quotaPercentage }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-primary-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: quotaPercentage + '%' }"
            ></div>
          </div>
          <div v-if="stats?.remaining_quota === 0" class="mt-2 text-sm text-red-600">
            ⚠️ 您的配额已用完，请联系管理员增加配额
          </div>
          <div v-else-if="quotaPercentage > 80" class="mt-2 text-sm text-orange-600">
            ⚠️ 您的配额即将用完，剩余 {{ stats?.remaining_quota }} 个
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg leading-6 font-medium text-gray-900">快速操作</h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <router-link
              to="/licenses/request"
              class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
            >
              <div class="flex-shrink-0">
                <PlusIcon class="h-6 w-6 text-primary-600" />
              </div>
              <div class="flex-1 min-w-0">
                <span class="absolute inset-0" aria-hidden="true" />
                <p class="text-sm font-medium text-gray-900">申请新授权</p>
                <p class="text-sm text-gray-500">为您的产品申请授权码</p>
              </div>
            </router-link>

            <router-link
              to="/licenses"
              class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
            >
              <div class="flex-shrink-0">
                <DocumentTextIcon class="h-6 w-6 text-primary-600" />
              </div>
              <div class="flex-1 min-w-0">
                <span class="absolute inset-0" aria-hidden="true" />
                <p class="text-sm font-medium text-gray-900">查看授权记录</p>
                <p class="text-sm text-gray-500">管理您的授权历史</p>
              </div>
            </router-link>

            <router-link
              to="/profile"
              class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
            >
              <div class="flex-shrink-0">
                <UserIcon class="h-6 w-6 text-primary-600" />
              </div>
              <div class="flex-1 min-w-0">
                <span class="absolute inset-0" aria-hidden="true" />
                <p class="text-sm font-medium text-gray-900">个人资料</p>
                <p class="text-sm text-gray-500">更新您的账户信息</p>
              </div>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'
import type { DashboardStats, ApiResponse } from '@/types'
import {
  CheckCircleIcon,
  DocumentTextIcon,
  CubeIcon,
  KeyIcon,
  PlusIcon,
  UserIcon,
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()
const stats = ref<DashboardStats | null>(null)

const quotaPercentage = computed(() => {
  if (!stats.value || stats.value.total_quota === 0) return 0
  return Math.round((stats.value.used_quota / stats.value.total_quota) * 100)
})

const fetchStats = async () => {
  try {
    const response = await api.get<ApiResponse<DashboardStats>>('/stats/dashboard')
    if (response.data.success) {
      stats.value = response.data.data!
    }
  } catch (error) {
    console.error('Failed to fetch dashboard stats:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>
